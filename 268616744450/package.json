{"name": "project_template_react", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev:client": "vite --host --port 3000", "dev": "pnpm dev:client", "build:client": "vite build --outDir dist/static", "build": "rm -rf dist && pnpm build:client && cp package.json dist && touch dist/build.flag"}, "dependencies": {"clsx": "^2.1.1", "framer-motion": "^12.9.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.3.0", "recharts": "^2.15.1", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "zod": "^3.24.2", "i18next": "^23.11.5", "react-i18next": "^14.1.2"}, "devDependencies": {"@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "vite": "^6.2.0", "vite-tsconfig-paths": "^5.1.4"}}