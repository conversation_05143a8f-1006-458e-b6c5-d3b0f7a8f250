import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { painPointsData } from '@/mock/data';

 const UserPainPoints: React.FC = () => {
   const { t } = useTranslation();
  // 痛点卡片动画变体
  const cardVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0, 
      scale: 1,
      transition: {
        delay: 0.2 * i,
        duration: 0.6,
        type: "spring",
        stiffness: 200,
        damping: 20
      }
    })
  };

  return (
    <motion.div 
      className="bg-white rounded-2xl shadow-xl p-8 h-full relative overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6 }}
    >
      {/* Decorative elements */}
      <div className="absolute -top-20 -right-20 w-64 h-64 bg-[var(--primary)]/5 rounded-full blur-3xl"></div>
      
      <h2 className="text-2xl font-bold text-center text-[var(--text-dark)] mb-8 relative z-10">
         {t('painPoints.title')}
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 relative z-10">
        {painPointsData.points.map((point, index) => (
          <motion.div 
            key={point.id} 
            className="border border-gray-100 rounded-xl bg-white p-6 flex flex-col items-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={cardVariants}
            custom={index}
          >
            <div className="w-16 h-full rounded-full bg-gradient-primary/10 flex items-center justify-center mb-4 group-hover:bg-gradient-primary/20 transition-colors duration-300">
              <span className="text-[var(--primary)] text-2xl">{point.icon}</span>
            </div>
             <h3 className="font-bold text-[var(--text-dark)] mb-3 text-center">{t(`painPoints.${point.title.toLowerCase()}`)}</h3>
            
             <p className="text-sm text-[var(--text-light)] text-center">{t(`painPoints.${point.title.toLowerCase()}Desc`)}
              {point.description}
            </p>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default UserPainPoints;
