import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { AuthContext } from '@/contexts/authContext';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';


interface HeaderProps {
  onLoginButtonClick: () => void;
}

const Header: React.FC<HeaderProps> = ({ onLoginButtonClick }) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  
  // 切换语言
  const toggleLanguage = () => {
    const newLanguage = i18n.language === 'zh' ? 'en' : 'zh';
    i18n.changeLanguage(newLanguage);
    localStorage.setItem('language', newLanguage);
  };
  const { isAuthenticated } = useContext(AuthContext);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  return (
    <header className={`header fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      scrolled ? 'bg-white/95 backdrop-blur-sm shadow-md py-2' : 'bg-transparent py-4'
    }`}>
      <div className="container mx-auto px-4 flex items-center justify-between">
        <motion.div 
          className="logo text-2xl font-bold"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <span className="text-gradient">即剪AI</span>
        </motion.div>
        
        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-8">
  <nav className="flex space-x-6">
    <Link to="/" className={`transition-colors font-medium ${location.pathname === '/' ? 'text-[var(--primary)]' : 'text-[var(--text-dark)] hover:text-[var(--primary)]'}`}>{t('header.home')}</Link>
    <Link to="/features" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">{t('header.features')}</Link>
    <Link to="/pricing" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">{t('header.pricing')}</Link>
    <Link to="/tutorials" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">{t('header.tutorials')}</Link>
  </nav>
           <div className="flex items-center space-x-3">
   <motion.button 
     className="px-4 py-2 bg-gradient-primary text-white rounded-lg hover:shadow-lg transition-all transform hover:-translate-y-0.5 text-sm font-medium"
     whileHover={{ scale: 1.03 }}
     whileTap={{ scale: 0.98 }}
     onClick={() => navigate('/video-upload')}
   >
     {t('header.longToShort')}
   </motion.button>
  <motion.button 
    className="px-4 py-2 bg-gray-100 text-[var(--text-dark)] rounded-lg hover:shadow-md transition-all transform hover:-translate-y-0.5 text-sm font-medium relative"
    whileHover={{ scale: 1.03 }}
    whileTap={{ scale: 0.98 }}
  >
    {t('header.mixedCut')}
    <span className="absolute -top-1 right-0 bg-gray-400 text-white text-xs px-2 py-0.5 rounded-full whitespace-nowrap">
      {t('header.comingSoon')}
    </span>
  </motion.button>
           </div>
           <div className="flex items-center ml-4">
             <motion.button 
               className="px-3 py-2 rounded-lg text-[var(--text-dark)] hover:bg-gray-100 transition-colors flex items-center"
               onClick={toggleLanguage}
               whileHover={{ scale: 1.05 }}
               whileTap={{ scale: 0.95 }}
             >
               {i18n.language === 'zh' ? '中文' : 'English'}
               <i className="fa-solid fa-globe ml-2"></i>
             </motion.button>
           </div>
          <div className="flex items-center space-x-4">
            {isAuthenticated && (
              <div className="hidden sm:block text-sm text-[var(--text-light)]">
                <span className="text-[var(--primary)] font-medium">234</span>  credits
              </div>
            )}
            <motion.button 
              className="px-5 py-2 bg-gradient-primary text-white rounded-lg hover:shadow-lg transition-all transform hover:-translate-y-0.5"
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
              onClick={onLoginButtonClick}
            >
  {isAuthenticated ? t('header.dashboard') : t('header.login')}
            </motion.button>
          </div>
        </div>
        
        {/* Mobile Menu Button */}
        <motion.button 
          className="md:hidden text-[var(--text-dark)] p-2 rounded-full hover:bg-gray-100 transition-colors"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <i className="fa-solid fa-bars text-xl"></i>
        </motion.button>
      </div>
      
      {/* Mobile Menu */}
      <motion.div 
        className="md:hidden bg-white border-t border-gray-100 px-4 py-3 shadow-lg"
        initial={{ height: 0, opacity: 0, overflow: 'hidden' }}
        animate={{ 
          height: mobileMenuOpen ? 'auto' : 0, 
          opacity: mobileMenuOpen ? 1 : 0,
          overflow: mobileMenuOpen ? 'visible' : 'hidden'
        }}
        transition={{ duration: 0.3, ease: [0.25, 0.1, 0.25, 1.0] }}
      >
  <nav className="flex flex-col space-y-4 py-3">
    <Link to="/" className={`py-2 border-b border-gray-100 transition-colors ${location.pathname === '/' ? 'text-[var(--primary)] font-medium' : 'text-[var(--text-dark)]'}`}>{t('header.home')}</Link>
    <Link to="/features" className="text-[var(--text-light)] py-2 border-b border-gray-100 hover:text-[var(--primary)] transition-colors">{t('header.features')}</Link>
    <Link to="/pricing" className="text-[var(--text-light)] py-2 border-b border-gray-100 hover:text-[var(--primary)] transition-colors">{t('header.pricing')}</Link>
    <Link to="/tutorials" className="text-[var(--text-light)] py-2 border-b border-gray-100 hover:text-[var(--primary)] transition-colors">{t('header.tutorials')}</Link>
  <button className="w-full py-3 bg-gradient-primary text-white rounded-lg hover:shadow-lg transition-all mb-2">
    {t('header.longToShort')}
  </button>
  <button className="w-full py-3 bg-gray-100 text-[var(--text-dark)] rounded-lg hover:shadow-md transition-all mb-2 relative">
      {t('header.mixedCut')}
      <span className="absolute top-2 right-4 bg-gray-400 text-white text-xs px-2 py-0.5 rounded-full whitespace-nowrap">
        {t('header.comingSoon')}
      </span>
    </button>
             <button 
               className="w-full py-3 border border-gray-200 rounded-lg text-[var(--text-dark)] hover:bg-gray-50 transition-colors flex items-center justify-center mb-2"
               onClick={toggleLanguage}
             >
               {i18n.language === 'zh' ? '切换到英文' : '切换到中文'}
               <i className="fa-solid fa-globe ml-2"></i>
             </button>
          {isAuthenticated && (
            <div className="text-sm text-[var(--text-light)] py-2 border-b border-gray-100">
              <span className="text-[var(--primary)] font-medium">234</span>  credits
            </div>
          )}
          <button 
            className="w-full py-3 bg-gradient-primary text-white rounded-lg hover:shadow-lg transition-all"
            onClick={onLoginButtonClick}
          >
  {isAuthenticated ? t('header.dashboard') : t('header.login')}
          </button>
        </nav>
      </motion.div>
    </header>
  );
};

export default Header;
