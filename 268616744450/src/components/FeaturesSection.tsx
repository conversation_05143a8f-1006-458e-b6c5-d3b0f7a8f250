import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { featuresData } from '@/mock/data';

 const FeaturesSection: React.FC = () => {
   const { t } = useTranslation();
  // 卡片动画变体
  const cardVariants = {
    hidden: { opacity: 0, y: 40, scale: 0.95 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        delay: 0.15 * i,
        duration: 0.6,
        type: "spring",
        stiffness: 200,
        damping: 20
      }
    })
  };

  return (
    <section className="features-section">
       <h2 className="text-3xl font-bold text-center text-[var(--text-dark)] mb-4">{t('features.title')}</h2>
      <p className="text-xl text-[var(--text-light)] text-center mb-12 max-w-2xl mx-auto">
       {t('features.subtitle')}
      </p>
      
      <div className="features-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {featuresData.map((feature, index) => (
          <motion.div 
            key={feature.id} 
            className="feature-card bg-white rounded-xl shadow-lg p-6 hover:transform hover:-translate-y-2 transition-all duration-500 hover:shadow-xl group relative overflow-hidden"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={cardVariants}
            custom={index}
          >
            {/* Decorative background element */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-[var(--primary)]/5 rounded-full -mr-16 -mt-16 group-hover:scale-150 transition-transform duration-700"></div>
            
            <div className="w-14 h-14 rounded-xl bg-gradient-primary/10 flex items-center justify-center mb-5 relative z-10">
              <i className={`fa-solid ${feature.icon} text-[var(--primary)] text-2xl`}></i>
            </div>
            <h3 className="text-xl font-bold text-[var(--text-dark)] mb-3 relative z-10">{feature.title}</h3>
            <p className="text-[var(--text-light)] relative z-10">{feature.description}</p>
            
            <motion.div 
              className="mt-4 text-[var(--primary)] font-medium flex items-center opacity-0 group-hover opacity-100 transition-opacity duration-300"
              initial={{ x: -10 }}
              whileHover={{ x: 5 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              了解更多 <i className="fa-solid fa-arrow-right ml-2"></i>
            </motion.div>
          </motion.div>
        ))}
      </div>
      
      <div className="text-center mt-16">
        <motion.button 
          className="px-8 py-4 bg-gradient-primary text-white rounded-lg hover:shadow-xl transition-all transform hover:-translate-y-1 text-lg font-medium"
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.98 }}
        >
           {t('features.startUsing')} <i className="fa-solid fa-arrow-right ml-2"></i>
        </motion.button>
      </div>
    </section>
  );
};

export default FeaturesSection;
