import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLoginSuccess: () => void;
}

const LoginModal: React.FC<LoginModalProps> = ({ isOpen, onClose, onLoginSuccess }) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ phone?: string; code?: string }>({});

  // 手机号验证
  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phone) {
      setErrors(prev => ({ ...prev, phone: '请输入手机号' }));
      return false;
    } else if (!phoneRegex.test(phone)) {
      setErrors(prev => ({ ...prev, phone: '请输入有效的手机号' }));
      return false;
    }
    setErrors(prev => ({ ...prev, phone: undefined }));
    return true;
  };

  // 验证码验证
  const validateCode = (code: string): boolean => {
    if (!code) {
      setErrors(prev => ({ ...prev, code: '请输入验证码' }));
      return false;
    } else if (code.length !== 6) {
      setErrors(prev => ({ ...prev, code: '验证码长度为6位' }));
      return false;
    }
    setErrors(prev => ({ ...prev, code: undefined }));
    return true;
  };

  // 获取验证码
  const handleGetCode = () => {
    if (!validatePhone(phoneNumber)) return;
    
    // 模拟获取验证码
    setCountdown(60);
    // 实际项目中这里会调用后端API发送验证码
  };

  // 登录处理
  const handleLogin = () => {
    if (!validatePhone(phoneNumber) || !validateCode(verificationCode)) return;
    
    setIsLoading(true);
    
    // 模拟登录请求
    setTimeout(() => {
      setIsLoading(false);
      // 模拟登录成功
      onLoginSuccess();
    }, 1500);
  };

  // 倒计时效果
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // 点击模态框外部关闭
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div 
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          onClick={handleOverlayClick}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* 背景遮罩 */}
          <motion.div 
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          />
          
          {/* 模态框内容 */}
          <motion.div 
            className="relative bg-white rounded-2xl shadow-2xl w-full max-w-md mx-auto overflow-hidden"
            initial={{ scale: 0.9, y: 20, opacity: 0 }}
            animate={{ scale: 1, y: 0, opacity: 1 }}
            exit={{ scale: 0.9, y: 20, opacity: 0 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          >
            {/* 关闭按钮 */}
            <button 
              onClick={onClose}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <i className="fa-solid fa-times text-xl"></i>
            </button>
            
            <div className="p-6 md:p-8">
              {/* 标题 */}
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-[var(--text-dark)] mb-2">手机号登录</h2>
                <p className="text-[var(--text-light)]">输入手机号和验证码进行登录</p>
              </div>
              
              {/* 表单 */}
              <div className="space-y-5">
                {/* 手机号输入 */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-[var(--text-dark)]">
                    手机号
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                      <i className="fa-solid fa-mobile-alt text-xl"></i>
                    </span>
                    <input
                      type="tel"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      onBlur={() => validatePhone(phoneNumber)}
                      className={cn(
                        "w-full pl-10 pr-4 py-3 rounded-lg border transition-all",
                        errors.phone 
                          ? "border-red-300 focus:border-red-500 focus:ring-red-500" 
                          : "border-gray-200 focus:border-[var(--primary)] focus:ring-[var(--primary)]"
                      )}
                      placeholder="请输入手机号"
                      maxLength={11}
                    />
                  </div>
                  {errors.phone && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <i className="fa-solid fa-exclamation-circle mr-1"></i>
                      {errors.phone}
                    </p>
                  )}
                </div>
                
                {/* 验证码输入 */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-[var(--text-dark)]">
                    验证码
                  </label>
                  <div className="flex gap-3">
                    <div className="relative flex-1">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                        <i className="fa-solid fa-shield-alt"></i>
                      </span>
                      <input
                        type="text"
                        value={verificationCode}
                        onChange={(e) => setVerificationCode(e.target.value)}
                        onBlur={() => validateCode(verificationCode)}
                        className={cn(
                          "w-full pl-10 pr-4 py-3 rounded-lg border transition-all",
                          errors.code 
                            ? "border-red-300 focus:border-red-500 focus:ring-red-500" 
                            : "border-gray-200 focus:border-[var(--primary)] focus:ring-[var(--primary)]"
                        )}
                        placeholder="请输入验证码"
                        maxLength={6}
                      />
                    </div>
                    
                    <button
                      onClick={handleGetCode}
                      disabled={countdown > 0 || !phoneNumber}
                      className={cn(
                        "px-4 py-3 rounded-lg text-sm font-medium transition-all whitespace-nowrap",
                        countdown > 0 || !phoneNumber
                          ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                          : "bg-[var(--primary)] text-white hover:bg-[var(--primary-dark)]"
                      )}
                    >
                      {countdown > 0 ? `重新发送(${countdown}s)` : "获取验证码"}
                    </button>
                  </div>
                  {errors.code && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <i className="fa-solid fa-exclamation-circle mr-1"></i>
                      {errors.code}
                    </p>
                  )}
                </div>
                
                {/* 登录按钮 */}
                <motion.button
                  onClick={handleLogin}
                  disabled={isLoading || !phoneNumber || !verificationCode || countdown === 0}
                  className={cn(
                    "w-full py-4 rounded-lg font-medium transition-all transform",
                    isLoading || !phoneNumber || !verificationCode || countdown === 0
                      ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                      : "bg-gradient-primary text-white hover:shadow-xl hover:-translate-y-0.5"
                  )}
                  whileHover={!isLoading && { scale: 1.02 }}
                  whileTap={!isLoading && { scale: 0.98 }}
                >
                  {isLoading ? (
                    <>
                      <i className="fa-solid fa-spinner fa-spin mr-2"></i>
                      登录中...
                    </>
                  ) : (
                    "登录"
                  )}
                </motion.button>
                
                {/* 其他登录方式 */}
                <div className="relative flex items-center justify-center mt-8">
                  <div className="absolute left-0 right-0 h-px bg-gray-200"></div>
                  <span className="relative bg-white px-4 text-sm text-gray-500">
                    其他登录方式
                  </span>
                </div>
                
            <div className="flex justify-center gap-6 mt-6">
              <button className="w-12 h-12 rounded-full border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-50 transition-colors">
                <i className="fa-brands fa-weixin text-xl"></i>
              </button>
              <button className="w-12 h-12 rounded-full border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-50 transition-colors">
                <i className="fa-brands fa-qq text-xl"></i>
              </button>
            </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default LoginModal;