import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { solutionsData } from '@/mock/data';

// 自然语言驱动剪辑解决方案
import { motion, useInView, useAnimation } from 'framer-motion';
import { useRef } from 'react';

 const NaturalLanguageSolution: React.FC = () => {
   const { t } = useTranslation();
  const { naturalLanguage } = solutionsData;
  const containerRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(containerRef, { once: true });
  const controls = useAnimation();
  
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  
  // 消息动画变体
  const messageVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.3 * i,
        duration: 0.3
      }
    })
  };
  
  // 视频动画变体
  const videoVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        delay: 1.5,
        duration: 0.5
      }
    }
  };
  
  return (
    <motion.div 
      ref={containerRef}
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
      }}
      className="bg-white rounded-xl shadow-[var(--shadow)] p-6 h-full hover:transform hover:-translate-y-1 transition-all duration-300"
    >
      <h2 className="text-xl font-bold text-center text-[var(--text-dark)] mb-2">
         {t('solutions.naturalLanguageTitle')}
      </h2>
      <p className="text-sm text-[var(--text-light)] text-center mb-4">
         {t('solutions.naturalLanguageSubtitle')}
      </p>
      
      <div className="flex flex-col md:flex-row items-center gap-4">
        <div className="border border-gray-100 rounded-lg bg-[var(--bg-light)] p-4 flex-1 w-full md:w-auto">
          {naturalLanguage.chatMessages.map((message, index) => (
            <motion.div 
              key={index} 
              className={`mb-2 ${message.isUser ? 'flex justify-end' : ''}`}
              initial="hidden"
              animate="visible"
              variants={messageVariants}
              custom={index}
            >
              <div 
                className={`px-3 py-2 rounded-md text-sm max-w-[80%] ${
                  message.isUser 
                    ? 'bg-[var(--primary)] text-white' 
                    : 'bg-white text-[var(--text-dark)] shadow-sm'
                }`}
              >
                {message.text}
              </div>
            </motion.div>
          ))}
        </div>
        
        <motion.div 
          className="text-[var(--primary)] text-xl"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.2, duration: 0.3 }}
        >
          →
        </motion.div>
        
        <motion.div 
          className="w-full md:w-48 h-32 bg-gray-800 rounded-lg flex items-center justify-center border border-gray-100 relative overflow-hidden flex-shrink-0"
          variants={videoVariants}
        >
          <img 
            src={naturalLanguage.videoUrl} 
            alt="视频预览" 
            className="w-full h-full object-cover"
          />
          <div className="absolute bottom-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
            03:24
          </div>
          <div className="absolute inset-0 flex items-center justify-center">
            <motion.div 
              className="w-10 h-10 rounded-full bg-white/80 flex items-center justify-center hover:bg-white transition-colors cursor-pointer"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <span className="text-black">▶</span>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};



// 解决方案展示容器组件
const SolutionsShowcase: React.FC = () => {
  return (
    
    <div className="space-y-8">
      <NaturalLanguageSolution />
    </div>
  );
};

export default SolutionsShowcase;
