import React, { useState, useEffect } from 'react';

import { motion } from 'framer-motion';

const InputSection: React.FC = () => {
  // 仅作为展示页面，移除实际生成功能
  const demoPrompt = "制作一个3分钟的产品介绍视频，突出产品特点和使用场景，风格要现代简洁...";
  
  return (
    <motion.section 
      className="input-section py-16 md:py-24 flex flex-col items-center justify-center"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
    >
      <div className="w-full max-w-4xl px-4">
        <h2 className="text-2xl md:text-3xl font-bold text-center text-[var(--text-dark)] mb-8">
          描述你的视频创意，AI 即刻生成
        </h2>
        
        <motion.textarea 
          className="prompt-input w-full h-40 md:h-48 p-4 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary)] resize-none transition-all"
          placeholder="描述你的视频创意..."
          value={demoPrompt}
          readOnly
          initial={{ opacity: 0, scale: 0.98 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        ></motion.textarea>
        
        <div className="mt-2 flex justify-between items-center">
          <div className="flex items-center">
            <div className="w-32 h-1 bg-gray-200 rounded-full overflow-hidden mr-2">
              <div 
                className="input-progress h-full bg-[var(--primary)] transition-all"
                style={{ width: "65%" }}
              ></div>
            </div>
            <span className="char-counter text-sm text-[var(--text-light)]">
              650/1000
            </span>
          </div>
        </div>
        
        <div className="input-actions flex flex-wrap gap-3 mt-4">
          <motion.button 
            className="action-btn url-btn px-4 py-2 border border-gray-200 rounded-lg text-[var(--text-dark)] hover:bg-gray-50 transition-colors flex items-center cursor-not-allowed"
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
            disabled
          >
            <i className="fa-solid fa-link mr-2"></i> Add URL
          </motion.button>
          <motion.button 
            className="action-btn upload-btn px-4 py-2 border border-gray-200 rounded-lg text-[var(--text-dark)] hover:bg-gray-50 transition-colors flex items-center cursor-not-allowed"
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.5 }}
            disabled
          >
            <i className="fa-solid fa-upload mr-2"></i> Upload
          </motion.button>
          <motion.button 
            className="generate-btn ml-auto px-6 py-2 bg-[var(--primary)] text-white rounded-lg hover:bg-[var(--primary-dark)] transition-all flex items-center font-medium cursor-not-allowed"
            initial={{ opacity: 0, x: 10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.6 }}
            disabled
          >
            <i className="fa-solid fa-play mr-2"></i> 演示生成过程
          </motion.button>
        </div>
      </div>
    </motion.section>
  );
};

export default InputSection;
