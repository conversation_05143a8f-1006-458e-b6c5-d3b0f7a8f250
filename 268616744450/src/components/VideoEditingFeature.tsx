import React from 'react';
import { videoEditingData } from '@/mock/data';

const VideoEditingFeature: React.FC = () => {
  return (
    <div className="bg-white rounded-xl shadow-[var(--shadow)] p-6 h-full">
      <h3 className="text-lg font-bold text-[var(--text-dark)] mb-4">智能视频编辑</h3>
      
      <div className="space-y-4">
        <div className="h-24 bg-[var(--bg-light)] rounded-lg overflow-hidden">
          <img 
            src={videoEditingData.timelineImage} 
            alt="视频时间线" 
            className="w-full h-full object-cover"
          />
        </div>
        
        <div className="flex gap-4">
          <div className="flex-1 h-24 bg-[var(--bg-light)] rounded-lg overflow-hidden">
            <img 
              src={videoEditingData.timelineImage} 
              alt="视频轨道" 
              className="w-full h-full object-cover"
            />
          </div>
          
          <div className="w-48 h-24 bg-gray-800 rounded-lg flex items-center justify-center relative overflow-hidden flex-shrink-0">
            <img 
              src={videoEditingData.videoPlayerImage} 
              alt="视频播放器" 
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-10 h-10 rounded-full bg-white/80 flex items-center justify-center hover:bg-white transition-colors cursor-pointer">
                <span className="text-black">▶</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoEditingFeature;
