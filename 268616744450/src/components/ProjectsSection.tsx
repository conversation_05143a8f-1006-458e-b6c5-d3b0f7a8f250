 import React from 'react';
 import { useTranslation } from 'react-i18next';
 import { motion } from 'framer-motion';
 import { projectsData } from '@/mock/data';
 import { Empty } from '@/components/Empty';

 const ProjectsSection: React.FC = () => {
   const { t } = useTranslation();
  // 项目卡片动画变体
  const cardVariants = {
    hidden: { opacity: 0, y: 40, scale: 0.95 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        delay: 0.1 * i,
        duration: 0.5,
        type: "spring",
        stiffness: 200,
        damping: 20
      }
    })
  };

  return (
    <section className="projects-section">
      <div className="flex flex-col md:flex-row md:items-end justify-between mb-8">
        <div>
           <h2 className="text-3xl font-bold text-[var(--text-dark)] mb-2">{t('projects.title')}</h2>
           <p className="text-[var(--text-light)]">{t('projects.subtitle')}</p>
        </div>
        <motion.button 
          className="mt-4 md:mt-0 text-[var(--primary)] hover:text-[var(--primary-dark)] transition-colors flex items-center font-medium"
          whileHover={{ x: 5 }}
          transition={{ type: "spring", stiffness: 300, damping: 20 }}
        >
           {t('projects.viewAll')} <i className="fa-solid fa-arrow-right ml-2"></i>
        </motion.button>
      </div>
      
       <div className="projects-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
         {projectsData.length > 0 ? (
           projectsData.map((project, index) => (
             <motion.div 
               key={project.id} 
               className="project-card bg-white rounded-xl shadow-lg overflow-hidden hover:transform hover:-translate-y-3 transition-all duration-500 hover:shadow-xl group"
               initial="hidden"
               whileInView="visible"
               viewport={{ once: true, margin: "-100px" }}
               variants={cardVariants}
               custom={index}
             >
               <div className="relative aspect-video bg-gray-100 overflow-hidden">
                 <motion.img 
                   src={project.thumbnail} 
                   alt={project.title} 
                   className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                 />
                 <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                   <motion.div 
                     className="w-14 h-14 rounded-full bg-white/90 flex items-center justify-center backdrop-blur-sm"
                     whileHover={{ scale: 1.1 }}
                     whileTap={{ scale: 0.9 }}
                   >
                     <i className="fa-solid fa-play text-[var(--primary)] text-xl"></i>
                   </motion.div>
                 </div>
                 <div className="absolute bottom-3 right-3 bg-black/60 text-white text-xs px-3 py-1 rounded-full backdrop-blur-sm">
                   {project.duration}
                 </div>
                 <div className="absolute top-3 left-3 bg-white/90 text-[var(--primary)] text-xs px-3 py-1 rounded-full backdrop-blur-sm font-medium">
                   热门
                 </div>
               </div>
               <div className="p-5">
                 <h3 className="font-bold text-[var(--text-dark)] mb-2 line-clamp-1 group-hover:text-[var(--primary)] transition-colors">{project.title}</h3>
                 <p className="text-sm text-[var(--text-light)] mb-4 line-clamp-2">{project.description}</p>
                 <div className="flex justify-between items-center">
                   <span className="text-xs text-[var(--text-light)]">{project.date}</span>
                   <div className="flex space-x-3">
                     <button className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors p-1.5 rounded-full hover:bg-[var(--primary)]/5">
                       <i className="fa-solid fa-heart"></i>
                     </button>
                     <button className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors p-1.5 rounded-full hover-bg-[var(--primary)]/5">
                       <i className="fa-solid fa-download"></i>
                     </button>
                     <button className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors p-1.5 rounded-full hover-bg-[var(--primary)]/5">
                       <i className="fa-solid fa-ellipsis-v"></i>
                     </button>
                   </div>
                 </div>
               </div>
             </motion.div>
           ))
         ) : (
           <div className="col-span-full flex justify-center items-center h-64">
             <Empty />
           </div>
         )}
       </div>
    </section>
  );
};

export default ProjectsSection;
