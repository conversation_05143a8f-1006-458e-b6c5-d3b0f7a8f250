  import React, { useEffect, useState, useContext } from 'react';
  import { useTranslation, Trans } from 'react-i18next';
 import { motion, useInView, useAnimation } from 'framer-motion';
 import { useRef } from 'react';
 import Header from '@/components/Header';
 import InputSection from '@/components/InputSection';
 import UserPainPoints from '@/components/UserPainPoints';
 import SolutionsShowcase from '@/components/SolutionsShowcase';
 import VideoEditingFeature from '@/components/VideoEditingFeature';
 import ProjectsSection from '@/components/ProjectsSection';
 import FeaturesSection from '@/components/FeaturesSection';
 import LoginModal from '@/components/LoginModal';
 import { AuthContext } from '@/contexts/authContext';
 

// 定义渐入动画组件
const FadeInSection: React.FC<{ children: React.ReactNode, delay?: number }> = ({ 
  children, 
  delay = 0 
}) => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: '-100px' });
  const controls = useAnimation();
  
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  
  return (
    <motion.div
      ref={sectionRef}
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0, y: 20 },
        visible: { 
          opacity: 1, 
          y: 0, 
          transition: { 
            duration: 0.6, 
            delay 
          } 
        }
      }}
    >
      {children}
    </motion.div>
  );
};

  export default function Home() {
    const { t } = useTranslation();
    const [showLoginModal, setShowLoginModal] = useState(false);
    const { setIsAuthenticated } = useContext(AuthContext);
   
   const handleLoginSuccess = () => {
     setIsAuthenticated(true);
     setShowLoginModal(false);
   };
   
   return (
     <div className="min-h-screen bg-[var(--bg-light)]">
       <Header onLoginButtonClick={() => setShowLoginModal(true)} />
       <LoginModal 
         isOpen={showLoginModal} 
         onClose={() => setShowLoginModal(false)} 
         onLoginSuccess={handleLoginSuccess} 
       />
      
      <main className="main-content pt-24">
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-gradient-to-b from-white to-[var(--bg-light)] py-16 md:py-24">
          <div className="absolute top-0 right-0 w-1/2 h-full bg-[var(--primary)]/5 rounded-l-full -z-10"></div>
          <div className="max-w-7xl mx-auto px-4 relative z-10">
            <FadeInSection delay={0.1}>
              <div className="text-center max-w-3xl mx-auto mb-12">
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                    <Trans i18nKey="hero.title">
                      <span className="text-gradient"></span>
                    </Trans>
                  </h1>
                 <p className="text-xl text-[var(--text-light)] mb-8">
                   {t('hero.subtitle')}
                 </p>
                 <div className="flex flex-col sm:flex-row justify-center gap-4">
                   <motion.button 
                     className="px-8 py-4 bg-gradient-primary text-white rounded-lg text-lg font-medium shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1"
                     whileHover={{ scale: 1.03 }}
                     whileTap={{ scale: 0.98 }}
                   >
                     {t('hero.tryFree')} <i className="fa-solid fa-arrow-right ml-2"></i>
                   </motion.button>
                   <motion.button 
                     className="px-8 py-4 bg-white text-[var(--text-dark)] rounded-lg text-lg font-medium shadow-md hover:shadow-lg transition-all flex items-center justify-center"
                     whileHover={{ scale: 1.03 }}
                     whileTap={{ scale: 0.98 }}
                   >
                     <i className="fa-solid fa-play-circle mr-2 text-[var(--primary)]"></i> {t('hero.watchDemo')}
                   </motion.button>
                </div>
              </div>
            </FadeInSection>
            
            <FadeInSection delay={0.3}>
              <div className="relative max-w-4xl mx-auto rounded-2xl overflow-hidden shadow-2xl border border-gray-100 transform md:rotate-1 animate-float">
                <img 
                  src="https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=AI%20video%20editing%20interface%20with%20chat%20interface%20and%20video%20preview&sign=9c1e812c0490578486ce1c585885db0c" 
                  alt="即剪AI界面展示" 
                  className="w-full h-auto"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent flex items-end">
                  <div className="p-6 text-white">
                     <p className="font-medium">{t('hero.videoPrompt')}</p>
                  </div>
                </div>
              </div>
            </FadeInSection>
          </div>
        </section>
        
        <div className="max-w-7xl mx-auto px-4 py-16">
          {/* 用户痛点和解决方案 */}
          <FadeInSection delay={0.1}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
              <div className="lg:col-span-2">
                <UserPainPoints />
              </div>
              <div className="lg:col-span-1">
                <VideoEditingFeature />
              </div>
            </div>
          </FadeInSection>
          
          {/* 解决方案展示 */}
          <FadeInSection delay={0.2}>
            <div className="mb-16">
               <h2 className="text-3xl font-bold text-center mb-12">{t('solutions.title')}</h2>
              <SolutionsShowcase />
            </div>
          </FadeInSection>
          
          {/* 项目展示区 */}
          <FadeInSection delay={0.3}>
            <div className="mb-16">
              <ProjectsSection />
            </div>
          </FadeInSection>
          
          {/* 功能介绍区 */}
          <FadeInSection delay={0.4}>
            <div>
              <FeaturesSection />
            </div>
          </FadeInSection>
          
          {/* CTA Section */}
          <FadeInSection delay={0.5}>
            <div className="mt-20 bg-gradient-primary rounded-2xl p-8 md:p-12 text-white text-center shadow-xl">
               <h2 className="text-3xl md:text-4xl font-bold mb-4">{t('cta.title')}</h2>
               <p className="text-xl mb-8 max-w-2xl mx-auto text-white/90">
                 {t('cta.subtitle')}
              </p>
              <motion.button 
                className="px-8 py-4 bg-white text-[var(--primary)] rounded-lg text-lg font-medium shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1"
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.98 }}
              >
                 {t('cta.getStarted')} <i className="fa-solid fa-rocket ml-2"></i>
              </motion.button>
            </div>
          </FadeInSection>
        </div>
      </main>
      
      <footer className="bg-white border-t border-gray-100 py-12 mt-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="logo text-2xl font-bold mb-4"><span className="text-gradient">即剪AI</span></div>
              <p className="text-[var(--text-light)] mb-4">用对话创造精彩视频，AI驱动的视频剪辑革命。</p>
              <div className="flex space-x-4">
                <a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"><i className="fa-brands fa-twitter"></i></a>
                <a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"><i className="fa-brands fa-instagram"></i></a>
                <a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"><i className="fa-brands fa-youtube"></i></a>
                <a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"><i className="fa-brands fa-github"></i></a>
              </div>
            </div>
            
            <div>
              <h3 className="font-bold text-lg mb-4">产品</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">功能</a></li>
                <li><a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">定价</a></li>
                <li><a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">教程</a></li>
                <li><a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">更新日志</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-bold text-lg mb-4">公司</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">关于我们</a></li>
                <li><a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">联系方式</a></li>
                <li><a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">招贤纳士</a></li>
                <li><a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">媒体资源</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-bold text-lg mb-4">法律</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">隐私政策</a></li>
                <li><a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">服务条款</a></li>
                <li><a href="#" className="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">版权声明</a></li>
              </ul>
            </div>
          </div>
          
          <div className="pt-8 border-t border-gray-100 flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm text-[var(--text-light)] mb-4 md:mb-0">
              © 2023 即剪AI. 保留所有权利.
            </div>
            <div className="flex space-x-6">
              <a href="#" className="text-sm text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">隐私政策</a>
              <a href="#" className="text-sm text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">服务条款</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
