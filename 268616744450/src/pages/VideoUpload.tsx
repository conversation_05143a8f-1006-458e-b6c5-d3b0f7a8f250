import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import Header from '@/components/Header';
import { useNavigate } from 'react-router-dom';

const VideoUpload: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [isDragging, setIsDragging] = useState(false);
  const [loginModalOpen, setLoginModalOpen] = useState(false);
  
  // 处理文件拖放事件
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };
  
  const handleDragLeave = () => {
    setIsDragging(false);
  };
  
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    // 这里处理上传的文件
    console.log('Files dropped:', e.dataTransfer.files);
  };
  
  // 处理文件选择
  const handleFileSelect = () => {
    // 在实际应用中，这里会触发文件选择对话框
    console.log('Select file clicked');
  };
  
  return (
    <div className="min-h-screen bg-[var(--bg-light)] flex flex-col">
      <Header onLoginButtonClick={() => setLoginModalOpen(true)} />
      
      <main className="flex-1 flex flex-col items-center justify-center p-4 pt-24">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-3xl font-bold text-[var(--text-dark)] mb-8 text-center">
              {t('videoUpload.title')}
            </h1>
            
            {/* 上传区域 */}
            <div 
              className={`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
                isDragging 
                  ? 'border-[var(--primary)] bg-[var(--primary)]/5 shadow-lg' 
                  : 'border-gray-300 hover:border-[var(--primary)] hover:bg-gray-50'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={handleFileSelect}
            >
              <div className="flex flex-col items-center justify-center">
                <div className="w-16 h-16 rounded-full bg-[var(--primary)]/10 flex items-center justify-center mb-4">
                  <i className="fa-solid fa-cloud-upload-alt text-[var(--primary)] text-2xl"></i>
                </div>
                <h3 className="text-xl font-medium text-[var(--text-dark)] mb-2">
                  {t('videoUpload.dragDrop')}
                </h3>
                <p className="text-[var(--text-light)] mb-6 max-w-md mx-auto">
                  {t('videoUpload.supportedFormats')}
                </p>
                
                <motion.button 
                  className="px-6 py-3 bg-gradient-primary text-white rounded-lg text-sm font-medium shadow-md hover:shadow-lg transition-all mb-6"
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {t('videoUpload.selectFile')}
                </motion.button>
                
                <div className="flex flex-wrap justify-center gap-3 mb-6">
                  <button className="px-4 py-2 bg-white border border-gray-200 rounded-lg text-sm text-[var(--text-dark)] hover:bg-gray-50 transition-colors flex items-center">
                    <i className="fa-brands fa-google mr-2"></i> Google Drive
                  </button>
                  <button className="px-4 py-2 bg-white border border-gray-200 rounded-lg text-sm text-[var(--text-dark)] hover:bg-gray-50 transition-colors flex items-center">
                    <i className="fa-brands fa-dropbox mr-2"></i> Dropbox
                  </button>
                  <button className="px-4 py-2 bg-white border border-gray-200 rounded-lg text-sm text-[var(--text-dark)] hover:bg-gray-50 transition-colors flex items-center">
                    <i className="fa-solid fa-server mr-2"></i> {t('videoUpload.cloudStorage')}
                  </button>
                </div>
                
                <p className="text-xs text-[var(--text-light)]">
                  {t('videoUpload.uploadLimit')}
                </p>
              </div>
            </div>
            
            {/* 先前上传的内容 */}
            <div className="mt-12">
              <h3 className="text-lg font-medium text-[var(--text-dark)] mb-4">
                {t('videoUpload.previousUploads')}
              </h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                {/* 示例上传项 */}
                <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow group">
                  <div className="relative aspect-video bg-gray-100">
                    <img 
                      src="https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Video%20thumbnail&sign=0062cff261c08ff3564c2c087c5b6fa0" 
                      alt="Video thumbnail" 
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                      <button className="w-10 h-10 rounded-full bg-white/90 flex items-center justify-center">
                        <i className="fa-solid fa-play text-[var(--primary)]"></i>
                      </button>
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                      04:23
                    </div>
                  </div>
                  <div className="p-3">
                    <h4 className="font-medium text-[var(--text-dark)] text-sm truncate">
                      {t('videoUpload.sampleVideo')}
                    </h4>
                    <p className="text-xs text-[var(--text-light)] mt-1">
                      {t('videoUpload.uploadedOn')} 2023-07-20
                    </p>
                  </div>
                </div>
                
                {/* 空状态提示 */}
                <div className="border border-dashed border-gray-200 rounded-lg aspect-video flex items-center justify-center text-[var(--text-light)]">
                  <i className="fa-solid fa-plus text-2xl mb-2 block"></i>
                  <span className="text-sm">{t('videoUpload.uploadNew')}</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </main>
      
      <footer className="bg-white border-t border-gray-100 py-8 mt-16">
        <div className="max-w-7xl mx-auto px-4 text-center text-sm text-[var(--text-light)]">
          {t('videoUpload.footerText')}
        </div>
      </footer>
    </div>
  );
};

export default VideoUpload;