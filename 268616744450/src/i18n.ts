import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// 导入翻译文件
import translationEN from './locales/en.json';
import translationZH from './locales/zh.json';

// 检测用户浏览器语言偏好或使用保存的偏好
const savedLanguage = localStorage.getItem('language');
const userLanguage = navigator.language.split('-')[0];
const initialLanguage = savedLanguage || (['zh', 'en'].includes(userLanguage) ? userLanguage : 'zh');

// 配置i18n
i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: translationEN },
      zh: { translation: translationZH }
    },
    lng: initialLanguage, // 设置初始语言
    fallbackLng: 'zh', // 回退语言
    interpolation: {
      escapeValue: false // 不需要转义
    }
  });

export default i18n;