<template>
  <div class="features-section">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-bold text-jijian-text-dark mb-4">强大功能特性</h2>
      <p class="text-xl text-jijian-text-light max-w-2xl mx-auto">
        即剪AI为您提供全方位的视频编辑解决方案，让创作变得简单高效
      </p>
    </div>

    <div class="features-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <div 
        v-for="(feature, index) in features" 
        :key="index"
        class="feature-item text-center group"
      >
        <div class="feature-icon-wrapper relative mb-6">
          <div class="feature-icon w-20 h-20 bg-jijian-primary/10 rounded-2xl flex items-center justify-center mx-auto group-hover:bg-jijian-primary transition-all duration-300">
            <el-icon class="text-3xl text-jijian-primary group-hover:text-white transition-colors duration-300" :class="feature.icon"></el-icon>
          </div>
          <div class="feature-glow absolute inset-0 bg-jijian-primary/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </div>
        
        <h3 class="text-xl font-semibold text-jijian-text-dark mb-3">{{ feature.title }}</h3>
        <p class="text-jijian-text-light mb-4">{{ feature.description }}</p>
        
        <ul class="feature-details space-y-2">
          <li 
            v-for="(detail, dIndex) in feature.details" 
            :key="dIndex"
            class="text-sm text-jijian-text-light flex items-center justify-center"
          >
            <el-icon class="mr-2 text-jijian-primary"><Check /></el-icon>
            {{ detail }}
          </li>
        </ul>
      </div>
    </div>

    <!-- 技术优势 -->
    <div class="tech-advantages mt-20">
      <div class="text-center mb-12">
        <h3 class="text-2xl font-bold text-jijian-text-dark mb-4">技术优势</h3>
        <p class="text-jijian-text-light">基于最新AI技术，为您提供卓越的视频编辑体验</p>
      </div>
      
      <div class="advantages-grid grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="advantage-card jijian-card p-6 text-center">
          <div class="advantage-number text-4xl font-bold text-jijian-primary mb-4">99.9%</div>
          <h4 class="font-semibold text-jijian-text-dark mb-2">识别准确率</h4>
          <p class="text-jijian-text-light text-sm">先进的AI算法确保高精度内容识别</p>
        </div>
        
        <div class="advantage-card jijian-card p-6 text-center">
          <div class="advantage-number text-4xl font-bold text-jijian-primary mb-4">10x</div>
          <h4 class="font-semibold text-jijian-text-dark mb-2">效率提升</h4>
          <p class="text-jijian-text-light text-sm">相比传统剪辑软件，效率提升10倍</p>
        </div>
        
        <div class="advantage-card jijian-card p-6 text-center">
          <div class="advantage-number text-4xl font-bold text-jijian-primary mb-4">24/7</div>
          <h4 class="font-semibold text-jijian-text-dark mb-2">云端服务</h4>
          <p class="text-jijian-text-light text-sm">全天候云端处理，随时随地访问</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Check } from '@element-plus/icons-vue'

const features = ref([
  {
    icon: 'ChatDotRound',
    title: 'AI智能剪辑',
    description: '通过自然语言描述，AI自动完成视频剪辑',
    details: ['语音识别', '意图理解', '智能执行']
  },
  {
    icon: 'MagicStick',
    title: '智能分段',
    description: '自动识别视频内容，智能分段处理',
    details: ['场景检测', '自动分割', '内容标记']
  },
  {
    icon: 'Download',
    title: '一键导出',
    description: '支持多种格式导出，满足不同平台需求',
    details: ['多格式支持', '批量导出', '质量优化']
  },
  {
    icon: 'CloudUpload',
    title: '云端同步',
    description: '项目自动云端保存，随时随地继续创作',
    details: ['实时同步', '版本管理', '团队协作']
  }
])
</script>

<style scoped>
.feature-item {
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-8px);
}

.feature-icon-wrapper {
  position: relative;
}

.feature-glow {
  z-index: -1;
}

.advantage-card {
  transition: all 0.3s ease;
}

.advantage-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(123, 97, 255, 0.15);
}

.advantage-number {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
</style>
