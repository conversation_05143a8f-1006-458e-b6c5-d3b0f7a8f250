<template>
  <!-- 功能特性展示 - 完全复制源项目 -->
  <section class="features-section">
    <h2 class="text-3xl font-bold text-center text-gray-900 mb-4">强大功能特性</h2>
    <p class="text-xl text-gray-600 text-center mb-12 max-w-2xl mx-auto">
      即剪AI为您提供全方位的视频编辑解决方案，让创作变得简单高效
    </p>

    <div class="features-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div class="feature-card bg-white rounded-xl shadow-lg p-6 hover:transform hover:-translate-y-2 transition-all duration-500 hover:shadow-xl group relative overflow-hidden">
        <div class="absolute top-0 right-0 w-32 h-32 bg-purple-100 rounded-full -mr-16 -mt-16 group-hover:scale-150 transition-transform duration-700"></div>
        <div class="w-14 h-14 rounded-xl bg-purple-100 flex items-center justify-center mb-5 relative z-10">
          <i class="fa-solid fa-magic text-purple-600 text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3 relative z-10">AI智能剪辑</h3>
        <p class="text-gray-600 relative z-10">通过自然语言描述，AI自动完成视频剪辑，无需复杂操作，让创意快速落地</p>
        <div class="mt-4 text-purple-600 font-medium flex items-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          了解更多 <i class="fa-solid fa-arrow-right ml-2"></i>
        </div>
      </div>

      <div class="feature-card bg-white rounded-xl shadow-lg p-6 hover:transform hover:-translate-y-2 transition-all duration-500 hover:shadow-xl group relative overflow-hidden">
        <div class="absolute top-0 right-0 w-32 h-32 bg-purple-100 rounded-full -mr-16 -mt-16 group-hover:scale-150 transition-transform duration-700"></div>
        <div class="w-14 h-14 rounded-xl bg-purple-100 flex items-center justify-center mb-5 relative z-10">
          <i class="fa-solid fa-comments text-purple-600 text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3 relative z-10">多轮对话优化</h3>
        <p class="text-gray-600 relative z-10">支持持续对话式修改，精确调整视频内容，让AI理解你的创意意图</p>
        <div class="mt-4 text-purple-600 font-medium flex items-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          了解更多 <i class="fa-solid fa-arrow-right ml-2"></i>
        </div>
      </div>

      <div class="feature-card bg-white rounded-xl shadow-lg p-6 hover:transform hover:-translate-y-2 transition-all duration-500 hover:shadow-xl group relative overflow-hidden">
        <div class="absolute top-0 right-0 w-32 h-32 bg-purple-100 rounded-full -mr-16 -mt-16 group-hover:scale-150 transition-transform duration-700"></div>
        <div class="w-14 h-14 rounded-xl bg-purple-100 flex items-center justify-center mb-5 relative z-10">
          <i class="fa-solid fa-cloud-upload-alt text-purple-600 text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3 relative z-10">素材智能匹配</h3>
        <p class="text-gray-600 relative z-10">自动分析文本内容，智能匹配最合适的视频素材，节省寻找素材的时间</p>
        <div class="mt-4 text-purple-600 font-medium flex items-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          了解更多 <i class="fa-solid fa-arrow-right ml-2"></i>
        </div>
      </div>

      <div class="feature-card bg-white rounded-xl shadow-lg p-6 hover:transform hover:-translate-y-2 transition-all duration-500 hover:shadow-xl group relative overflow-hidden">
        <div class="absolute top-0 right-0 w-32 h-32 bg-purple-100 rounded-full -mr-16 -mt-16 group-hover:scale-150 transition-transform duration-700"></div>
        <div class="w-14 h-14 rounded-xl bg-purple-100 flex items-center justify-center mb-5 relative z-10">
          <i class="fa-solid fa-film text-purple-600 text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3 relative z-10">多风格模板</h3>
        <p class="text-gray-600 relative z-10">内置多种视频风格模板，一键应用专业级滤镜和转场效果，提升视频质感</p>
        <div class="mt-4 text-purple-600 font-medium flex items-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          了解更多 <i class="fa-solid fa-arrow-right ml-2"></i>
        </div>
      </div>

      <div class="feature-card bg-white rounded-xl shadow-lg p-6 hover:transform hover:-translate-y-2 transition-all duration-500 hover:shadow-xl group relative overflow-hidden">
        <div class="absolute top-0 right-0 w-32 h-32 bg-purple-100 rounded-full -mr-16 -mt-16 group-hover:scale-150 transition-transform duration-700"></div>
        <div class="w-14 h-14 rounded-xl bg-purple-100 flex items-center justify-center mb-5 relative z-10">
          <i class="fa-solid fa-users text-purple-600 text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3 relative z-10">协作编辑</h3>
        <p class="text-gray-600 relative z-10">支持多人实时协作，评论和建议直接同步，团队创作更高效</p>
        <div class="mt-4 text-purple-600 font-medium flex items-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          了解更多 <i class="fa-solid fa-arrow-right ml-2"></i>
        </div>
      </div>

      <div class="feature-card bg-white rounded-xl shadow-lg p-6 hover:transform hover:-translate-y-2 transition-all duration-500 hover:shadow-xl group relative overflow-hidden">
        <div class="absolute top-0 right-0 w-32 h-32 bg-purple-100 rounded-full -mr-16 -mt-16 group-hover:scale-150 transition-transform duration-700"></div>
        <div class="w-14 h-14 rounded-xl bg-purple-100 flex items-center justify-center mb-5 relative z-10">
          <i class="fa-solid fa-bolt text-purple-600 text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3 relative z-10">快速导出</h3>
        <p class="text-gray-600 relative z-10">多格式快速导出，适配各大社交平台，一键分享你的创作成果</p>
        <div class="mt-4 text-purple-600 font-medium flex items-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          了解更多 <i class="fa-solid fa-arrow-right ml-2"></i>
        </div>
      </div>
    </div>

    <div class="text-center mt-16">
      <button class="px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:shadow-xl transition-all transform hover:-translate-y-1 hover:scale-105 text-lg font-medium">
        开始使用 <i class="fa-solid fa-arrow-right ml-2"></i>
      </button>
    </div>
  </section>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { featuresData } from '@/mock/data'

const { t } = useI18n()
</script>

<style scoped>
/* 完全复制源项目样式 */
.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}
</style>
