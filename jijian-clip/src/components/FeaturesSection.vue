<template>
  <!-- 完全复制源项目的FeaturesSection组件 -->
  <section class="features-section">
    <h2 class="text-3xl font-bold text-center text-[var(--text-dark)] mb-4">{{ $t('features.title') }}</h2>
    <p class="text-xl text-[var(--text-light)] text-center mb-12 max-w-2xl mx-auto">
      {{ $t('features.subtitle') }}
    </p>

    <div class="features-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div
        v-for="(feature, index) in featuresData"
        :key="feature.id"
        class="feature-card bg-white rounded-xl shadow-lg p-6 hover:transform hover:-translate-y-2 transition-all duration-500 hover:shadow-xl group relative overflow-hidden"
        v-motion
        :initial="{ opacity: 0, y: 40, scale: 0.95 }"
        :whileInView="{
          opacity: 1,
          y: 0,
          scale: 1,
          transition: {
            delay: 0.15 * index,
            duration: 600,
            type: 'spring',
            stiffness: 200,
            damping: 20
          }
        }"
        :viewport="{ once: true, margin: '-100px' }"
      >
        <!-- Decorative background element -->
        <div class="absolute top-0 right-0 w-32 h-32 bg-[var(--primary)]/5 rounded-full -mr-16 -mt-16 group-hover:scale-150 transition-transform duration-700"></div>

        <div class="w-14 h-14 rounded-xl bg-gradient-primary/10 flex items-center justify-center mb-5 relative z-10">
          <i :class="`fa-solid ${feature.icon} text-[var(--primary)] text-2xl`"></i>
        </div>
        <h3 class="text-xl font-bold text-[var(--text-dark)] mb-3 relative z-10">{{ feature.title }}</h3>
        <p class="text-[var(--text-light)] relative z-10">{{ feature.description }}</p>

        <div
          class="mt-4 text-[var(--primary)] font-medium flex items-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          v-motion
          :initial="{ x: -10 }"
          :whileHover="{ x: 5 }"
          :transition="{ type: 'spring', stiffness: 300, damping: 20 }"
        >
          了解更多 <i class="fa-solid fa-arrow-right ml-2"></i>
        </div>
      </div>
    </div>

    <div class="text-center mt-16">
      <button
        class="px-8 py-4 bg-gradient-primary text-white rounded-lg hover:shadow-xl transition-all transform hover:-translate-y-1 text-lg font-medium"
        v-motion
        :whileHover="{ scale: 1.03 }"
        :whileTap="{ scale: 0.98 }"
      >
        {{ $t('features.startUsing') }} <i class="fa-solid fa-arrow-right ml-2"></i>
      </button>
    </div>
  </section>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { featuresData } from '@/mock/data'

const { t } = useI18n()
</script>

<style scoped>
/* 完全复制源项目样式 */
.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}
</style>
