<template>
  <!-- 完全复制源项目的VideoEditingFeature组件 -->
  <div class="bg-white rounded-xl shadow-[var(--shadow)] p-6 h-full">
    <h3 class="text-lg font-bold text-[var(--text-dark)] mb-4">智能视频编辑</h3>

    <div class="space-y-4">
      <!-- 时间线图片 -->
      <div class="h-24 bg-[var(--bg-light)] rounded-lg overflow-hidden">
        <img
          :src="videoEditingData.timelineImage"
          alt="视频时间线"
          class="w-full h-full object-cover"
        />
      </div>

      <!-- 视频轨道和播放器 -->
      <div class="flex gap-4">
        <div class="flex-1 h-24 bg-[var(--bg-light)] rounded-lg overflow-hidden">
          <img
            :src="videoEditingData.timelineImage"
            alt="视频轨道"
            class="w-full h-full object-cover"
          />
        </div>

        <div class="w-48 h-24 bg-gray-800 rounded-lg flex items-center justify-center relative overflow-hidden flex-shrink-0">
          <img
            :src="videoEditingData.videoPlayerImage"
            alt="视频播放器"
            class="w-full h-full object-cover"
          />
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="w-10 h-10 rounded-full bg-white/80 flex items-center justify-center hover:bg-white transition-colors cursor-pointer">
              <span class="text-black">▶</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { videoEditingData } from '@/mock/data'
</script>

<style scoped>
/* 完全复制源项目样式 */
</style>
