<template>
  <div class="video-editing-feature jijian-card p-8 bg-gradient-primary text-white">
    <div class="text-center">
      <div class="feature-icon w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
        <el-icon class="text-3xl"><VideoCamera /></el-icon>
      </div>
      <h3 class="text-xl font-bold mb-4">AI智能剪辑</h3>
      <p class="text-white/90 mb-6">
        通过自然语言对话，AI自动理解您的需求，完成专业级视频剪辑
      </p>
      <ul class="text-left space-y-2 text-white/80">
        <li class="flex items-center">
          <el-icon class="mr-2"><Check /></el-icon>
          智能场景识别
        </li>
        <li class="flex items-center">
          <el-icon class="mr-2"><Check /></el-icon>
          自动剪辑建议
        </li>
        <li class="flex items-center">
          <el-icon class="mr-2"><Check /></el-icon>
          一键生成字幕
        </li>
        <li class="flex items-center">
          <el-icon class="mr-2"><Check /></el-icon>
          智能配乐匹配
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { VideoCamera, Check } from '@element-plus/icons-vue'
</script>

<style scoped>
.feature-icon {
  transition: all 0.3s ease;
}

.video-editing-feature:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}
</style>
