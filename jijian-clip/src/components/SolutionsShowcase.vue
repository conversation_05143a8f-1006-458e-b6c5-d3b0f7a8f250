<template>
  <!-- 自然语言驱动剪辑解决方案 - 完全复制源项目 -->
  <div class="space-y-8">
    <div
      ref="containerRef"
      class="bg-white rounded-xl shadow-[var(--shadow)] p-6 h-full hover:transform hover:-translate-y-1 transition-all duration-300"
      v-motion
      :initial="{ opacity: 0, y: 20 }"
      :whileInView="{ opacity: 1, y: 0, transition: { duration: 500 } }"
      :viewport="{ once: true }"
    >
      <h2 class="text-xl font-bold text-center text-[var(--text-dark)] mb-2">
        {{ $t('solutions.naturalLanguageTitle') }}
      </h2>
      <p class="text-sm text-[var(--text-light)] text-center mb-4">
        {{ $t('solutions.naturalLanguageSubtitle') }}
      </p>

      <div class="flex flex-col md:flex-row items-center gap-4">
        <!-- 聊天界面 -->
        <div class="border border-gray-100 rounded-lg bg-[var(--bg-light)] p-4 flex-1 w-full md:w-auto">
          <div
            v-for="(message, index) in naturalLanguage.chatMessages"
            :key="index"
            :class="['mb-2', message.isUser ? 'flex justify-end' : '']"
            v-motion
            :initial="{ opacity: 0, y: 10 }"
            :enter="{
              opacity: 1,
              y: 0,
              transition: { delay: 0.3 * index, duration: 300 }
            }"
          >
            <div
              :class="[
                'px-3 py-2 rounded-md text-sm max-w-[80%]',
                message.isUser
                  ? 'bg-[var(--primary)] text-white'
                  : 'bg-white text-[var(--text-dark)] shadow-sm'
              ]"
            >
              {{ message.text }}
            </div>
          </div>
        </div>

        <!-- 箭头 -->
        <div
          class="text-[var(--primary)] text-xl"
          v-motion
          :initial="{ opacity: 0 }"
          :enter="{ opacity: 1, transition: { delay: 1200, duration: 300 } }"
        >
          →
        </div>

        <!-- 视频预览 -->
        <div
          class="w-full md:w-48 h-32 bg-gray-800 rounded-lg flex items-center justify-center border border-gray-100 relative overflow-hidden flex-shrink-0"
          v-motion
          :initial="{ opacity: 0, scale: 0.9 }"
          :enter="{
            opacity: 1,
            scale: 1,
            transition: { delay: 1500, duration: 500 }
          }"
        >
          <img
            :src="naturalLanguage.videoUrl"
            alt="视频预览"
            class="w-full h-full object-cover"
          />
          <div class="absolute bottom-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
            03:24
          </div>
          <div class="absolute inset-0 flex items-center justify-center">
            <div
              class="w-10 h-10 rounded-full bg-white/80 flex items-center justify-center hover:bg-white transition-colors cursor-pointer"
              v-motion
              :whileHover="{ scale: 1.1 }"
              :whileTap="{ scale: 0.9 }"
            >
              <span class="text-black">▶</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { solutionsData } from '@/mock/data'

const { t } = useI18n()
const { naturalLanguage } = solutionsData
const containerRef = ref<HTMLDivElement>()
</script>

<style scoped>
/* 完全复制源项目样式 */
</style>
