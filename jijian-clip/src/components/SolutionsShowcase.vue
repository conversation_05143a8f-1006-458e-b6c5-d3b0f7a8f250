<template>
  <!-- 解决方案展示 - 完全复制源项目 -->
  <div class="bg-white rounded-xl shadow-[var(--shadow)] p-6 h-full hover:transform hover:-translate-y-1 transition-all duration-300">
    <h2 class="text-xl font-bold text-center text-[var(--text-dark)] mb-2">
      解决方案：自然语言驱动剪辑
    </h2>
    <p class="text-sm text-[var(--text-light)] text-center mb-4">
      简单对话完成视频创作
    </p>

    <div class="flex flex-col md:flex-row items-center gap-4">
      <!-- 聊天界面 -->
      <div class="border border-gray-100 rounded-lg bg-[var(--bg-light)] p-4 flex-1 w-full md:w-auto">
        <div class="mb-2 flex justify-end">
          <div class="px-3 py-2 rounded-md text-sm max-w-[80%] bg-[var(--primary)] text-white">
            制作一个三分钟的产品视频
          </div>
        </div>
        <div class="mb-2">
          <div class="px-3 py-2 rounded-md text-sm max-w-[80%] bg-white text-[var(--text-dark)] shadow-sm">
            正在生成视频脚本：
          </div>
        </div>
        <div class="mb-2">
          <div class="px-3 py-2 rounded-md text-sm max-w-[80%] bg-white text-[var(--text-dark)] shadow-sm">
            1. 产品介绍 (0-30s)
          </div>
        </div>
        <div class="mb-2">
          <div class="px-3 py-2 rounded-md text-sm max-w-[80%] bg-white text-[var(--text-dark)] shadow-sm">
            正在挑选片段：......
          </div>
        </div>
        <div class="mb-2">
          <div class="px-3 py-2 rounded-md text-sm max-w-[80%] bg-white text-[var(--text-dark)] shadow-sm">
            已生成三分钟的产品视频
          </div>
        </div>
      </div>

      <!-- 箭头 -->
      <div class="text-[var(--primary)] text-xl">
        →
      </div>

      <!-- 视频预览 -->
      <div class="w-full md:w-48 h-32 bg-gray-800 rounded-lg flex items-center justify-center border border-gray-100 relative overflow-hidden flex-shrink-0">
        <img
          src="https://space.coze.cn/api/coze_space/gen_image?image_size=square&prompt=Video%20player%20interface&sign=f872cc210b5be188eb68961c0119469c"
          alt="视频预览"
          class="w-full h-full object-cover"
        />
        <div class="absolute bottom-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
          03:24
        </div>
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="w-10 h-10 rounded-full bg-white/80 flex items-center justify-center hover:bg-white transition-colors cursor-pointer hover:scale-110">
            <span class="text-black">▶</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 解决方案展示组件 - 完全复制源项目内容
</script>

<style scoped>
/* 完全复制源项目样式 */
</style>
