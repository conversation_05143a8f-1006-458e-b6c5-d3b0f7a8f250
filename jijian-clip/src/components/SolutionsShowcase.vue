<template>
  <div class="solutions-showcase">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div 
        v-for="(solution, index) in solutions" 
        :key="index"
        class="solution-card jijian-card p-6 text-center hover:shadow-xl transition-all duration-300"
      >
        <div class="solution-icon w-16 h-16 bg-jijian-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <el-icon class="text-2xl text-jijian-primary" :class="solution.icon"></el-icon>
        </div>
        <h4 class="text-xl font-semibold text-jijian-text-dark mb-3">{{ solution.title }}</h4>
        <p class="text-jijian-text-light mb-4">{{ solution.description }}</p>
        <div class="features-list">
          <div 
            v-for="(feature, fIndex) in solution.features" 
            :key="fIndex"
            class="feature-tag inline-block bg-jijian-bg-light text-jijian-text-dark px-3 py-1 rounded-full text-sm mr-2 mb-2"
          >
            {{ feature }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const solutions = ref([
  {
    icon: 'ChatDotRound',
    title: '对话式剪辑',
    description: '用自然语言描述您的需求，AI自动完成视频剪辑',
    features: ['语音识别', '意图理解', '智能执行']
  },
  {
    icon: 'MagicStick',
    title: '智能分析',
    description: '自动识别视频内容，提供专业的剪辑建议',
    features: ['场景检测', '人物识别', '情感分析']
  },
  {
    icon: 'Lightning',
    title: '快速导出',
    description: '一键导出多种格式，满足不同平台需求',
    features: ['多格式支持', '批量处理', '云端渲染']
  }
])
</script>

<style scoped>
.solution-card:hover {
  transform: translateY(-8px);
}

.solution-icon {
  transition: all 0.3s ease;
}

.solution-card:hover .solution-icon {
  transform: scale(1.1);
  background-color: var(--primary);
}

.solution-card:hover .solution-icon .el-icon {
  color: white;
}

.feature-tag {
  transition: all 0.3s ease;
}

.feature-tag:hover {
  background-color: var(--primary);
  color: white;
  transform: scale(1.05);
}
</style>
