<template>
  <div class="video-showcase relative py-8">
    <!-- 主视频展示区域 -->
    <div class="main-video-container relative max-w-7xl mx-auto">
      <!-- 编辑器预览 -->
      <VideoEditorPreview />


      <!-- 装饰性元素 -->
      <div class="absolute -top-8 -right-8 w-32 h-32 bg-[var(--primary)]/10 rounded-full blur-2xl"></div>
      <div class="absolute -bottom-8 -left-8 w-24 h-24 bg-blue-500/10 rounded-full blur-xl"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import VideoEditorPreview from './VideoEditorPreview.vue'

const emit = defineEmits<{
  playDemo: []
}>()

const handlePlayDemo = () => {
  emit('playDemo')
}
</script>

<style scoped>
/* 简化样式，主要依赖VideoEditorPreview组件 */
</style>
