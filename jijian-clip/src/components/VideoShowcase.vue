<template>
  <div class="video-showcase relative py-12">
    <!-- 主视频展示区域 -->
    <div class="main-video-container relative max-w-6xl mx-auto">
      <!-- 现代化视频播放器 -->
      <div class="video-player-wrapper relative">
        <!-- 主视频容器 -->
        <div class="video-container relative bg-gradient-to-br from-gray-900 via-gray-800 to-black rounded-3xl overflow-hidden shadow-2xl border border-gray-700/50">
          <!-- 视频画面区域 -->
          <div class="video-display aspect-video relative">
            <!-- 背景渐变 -->
            <div class="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20"></div>

            <!-- 主要内容区域 -->
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="text-center text-white z-10">
                <!-- 播放按钮 -->
                <div
                  class="play-button w-20 h-20 mx-auto mb-6 bg-white/10 backdrop-blur-md rounded-full flex items-center justify-center cursor-pointer hover:bg-white/20 transition-all duration-300 group"
                  @click="handlePlayDemo"
                >
                  <div class="w-16 h-16 bg-[var(--primary)] rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fa-solid fa-play text-white text-xl ml-1"></i>
                  </div>
                </div>

                <!-- 标题和描述 -->
                <h3 class="text-2xl font-bold mb-3 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  体验AI智能剪辑
                </h3>
                <p class="text-white/80 text-lg mb-6 max-w-md mx-auto">
                  观看即剪AI如何将普通视频转化为专业作品
                </p>

                <!-- 特性标签 -->
                <div class="flex flex-wrap justify-center gap-3">
                  <span class="feature-tag bg-white/10 backdrop-blur-sm text-white text-sm px-3 py-1 rounded-full border border-white/20">
                    <i class="fa-solid fa-magic-wand-sparkles mr-1 text-[var(--primary)]"></i>
                    AI智能识别
                  </span>
                  <span class="feature-tag bg-white/10 backdrop-blur-sm text-white text-sm px-3 py-1 rounded-full border border-white/20">
                    <i class="fa-solid fa-scissors mr-1 text-green-400"></i>
                    自动剪辑
                  </span>
                  <span class="feature-tag bg-white/10 backdrop-blur-sm text-white text-sm px-3 py-1 rounded-full border border-white/20">
                    <i class="fa-solid fa-music mr-1 text-blue-400"></i>
                    智能配乐
                  </span>
                </div>
              </div>
            </div>

            <!-- 装饰性粒子效果 -->
            <div class="particles absolute inset-0 overflow-hidden pointer-events-none">
              <div class="particle absolute w-2 h-2 bg-white/20 rounded-full animate-float-1"></div>
              <div class="particle absolute w-1 h-1 bg-[var(--primary)]/40 rounded-full animate-float-2"></div>
              <div class="particle absolute w-1.5 h-1.5 bg-blue-400/30 rounded-full animate-float-3"></div>
              <div class="particle absolute w-1 h-1 bg-purple-400/30 rounded-full animate-float-4"></div>
            </div>

            <!-- 状态指示器 -->
            <div class="absolute top-6 left-6">
              <div class="status-indicator flex items-center space-x-2 bg-black/50 backdrop-blur-sm rounded-full px-4 py-2">
                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span class="text-white text-sm font-medium">AI就绪</span>
              </div>
            </div>

            <!-- 质量标识 -->
            <div class="absolute top-6 right-6">
              <div class="quality-badge bg-gradient-to-r from-[var(--primary)] to-purple-600 text-white text-xs font-bold px-3 py-1 rounded-full">
                4K HDR
              </div>
            </div>

            <!-- 时长显示 -->
            <div class="absolute bottom-6 right-6">
              <div class="duration-display bg-black/70 backdrop-blur-sm text-white text-sm font-mono px-3 py-1 rounded-full">
                02:34
              </div>
            </div>
          </div>

          <!-- 底部控制栏 -->
          <div class="control-bar bg-gradient-to-r from-gray-900/95 to-black/95 backdrop-blur-sm p-4 border-t border-gray-700/50">
            <div class="flex items-center justify-between">
              <!-- 左侧控制 -->
              <div class="flex items-center space-x-4">
                <button class="control-btn" @click="handlePlayDemo" title="播放演示">
                  <i class="fa-solid fa-play"></i>
                </button>
                <button class="control-btn" title="全屏">
                  <i class="fa-solid fa-expand"></i>
                </button>
                <button class="control-btn" title="音量">
                  <i class="fa-solid fa-volume-up"></i>
                </button>
                <span class="text-white/70 text-sm">即剪AI演示</span>
              </div>

              <!-- 右侧信息 -->
              <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2 text-white/70 text-sm">
                  <i class="fa-solid fa-eye"></i>
                  <span>1.2k 观看</span>
                </div>
                <div class="flex items-center space-x-2 text-white/70 text-sm">
                  <i class="fa-solid fa-heart text-red-400"></i>
                  <span>89</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 浮动装饰元素 -->
        <div class="floating-elements absolute inset-0 pointer-events-none">
          <!-- 光晕效果 -->
          <div class="absolute -top-20 -right-20 w-40 h-40 bg-[var(--primary)]/10 rounded-full blur-3xl animate-pulse"></div>
          <div class="absolute -bottom-16 -left-16 w-32 h-32 bg-blue-500/10 rounded-full blur-2xl animate-pulse" style="animation-delay: 1s"></div>
          <div class="absolute top-1/2 -right-8 w-24 h-24 bg-purple-500/10 rounded-full blur-xl animate-pulse" style="animation-delay: 2s"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits<{
  playDemo: []
}>()

const handlePlayDemo = () => {
  emit('playDemo')
}
</script>

<style scoped>
/* 现代化视频预览样式 */
.video-showcase {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.video-container {
  transform: perspective(1000px) rotateX(2deg);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.video-container:hover {
  transform: perspective(1000px) rotateX(0deg) translateY(-8px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

/* 播放按钮动画 */
.play-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.play-button:hover {
  transform: scale(1.1);
  box-shadow: 0 0 30px rgba(var(--primary-rgb), 0.5);
}

/* 特性标签动画 */
.feature-tag {
  transition: all 0.3s ease;
  animation: fadeInUp 0.6s ease-out forwards;
}

.feature-tag:nth-child(1) { animation-delay: 0.1s; }
.feature-tag:nth-child(2) { animation-delay: 0.2s; }
.feature-tag:nth-child(3) { animation-delay: 0.3s; }

.feature-tag:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 控制按钮样式 */
.control-btn {
  @apply w-10 h-10 bg-white/10 hover:bg-white/20 text-white rounded-lg flex items-center justify-center transition-all duration-200 backdrop-blur-sm border border-white/10;
}

.control-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 粒子动画 */
.particles .particle:nth-child(1) {
  top: 20%;
  left: 15%;
}

.particles .particle:nth-child(2) {
  top: 60%;
  right: 20%;
}

.particles .particle:nth-child(3) {
  bottom: 30%;
  left: 25%;
}

.particles .particle:nth-child(4) {
  top: 40%;
  right: 35%;
}

/* 浮动动画 */
@keyframes float-1 {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(-10px) translateX(5px); }
  50% { transform: translateY(-5px) translateX(-5px); }
  75% { transform: translateY(-15px) translateX(3px); }
}

@keyframes float-2 {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  33% { transform: translateY(-8px) translateX(-3px); }
  66% { transform: translateY(-12px) translateX(4px); }
}

@keyframes float-3 {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  40% { transform: translateY(-6px) translateX(2px); }
  80% { transform: translateY(-10px) translateX(-4px); }
}

@keyframes float-4 {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  50% { transform: translateY(-7px) translateX(-2px); }
}

.animate-float-1 { animation: float-1 6s ease-in-out infinite; }
.animate-float-2 { animation: float-2 8s ease-in-out infinite; }
.animate-float-3 { animation: float-3 7s ease-in-out infinite; }
.animate-float-4 { animation: float-4 9s ease-in-out infinite; }

/* 淡入上升动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 状态指示器动画 */
.status-indicator {
  animation: slideInLeft 0.8s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 质量标识动画 */
.quality-badge {
  animation: slideInRight 0.8s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 时长显示动画 */
.duration-display {
  animation: fadeIn 1s ease-out 0.5s both;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-container {
    transform: none;
    margin: 0 1rem;
  }

  .video-container:hover {
    transform: translateY(-4px);
  }

  .play-button {
    width: 4rem;
    height: 4rem;
  }

  .feature-tag {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
  }

  .control-bar {
    padding: 0.75rem;
  }

  .floating-elements {
    display: none;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .video-showcase {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  }
}
</style>
