<template>
  <div class="video-editor-preview bg-[#1b1b1b] rounded-2xl overflow-hidden shadow-2xl border border-gray-700">
    <!-- 顶部工具栏 -->
    <div class="editor-toolbar bg-[#2a2a2a] px-4 py-3 border-b border-gray-700">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
          </div>
          <span class="text-white text-sm font-medium">即剪AI - 智能视频编辑器</span>
        </div>
        <div class="flex items-center space-x-2 text-white text-sm">
          <i class="fa-solid fa-magic-wand-sparkles text-[var(--primary)]"></i>
          <span>AI处理中...</span>
        </div>
      </div>
    </div>

    <!-- 主编辑区域 -->
    <div class="editor-main flex">
      <!-- 左侧面板 -->
      <div class="left-panel w-64 bg-[#1e1e1e] border-r border-gray-700">
        <!-- 素材库 -->
        <div class="panel-section p-4">
          <h3 class="text-white text-sm font-medium mb-3">素材库</h3>
          <div class="space-y-2">
            <div class="media-item bg-gray-800 rounded p-2 flex items-center space-x-2 hover:bg-gray-700 transition-colors cursor-pointer">
              <div class="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                <i class="fa-solid fa-video text-white text-xs"></i>
              </div>
              <span class="text-white text-xs">产品介绍.mp4</span>
            </div>
            <div class="media-item bg-gray-800 rounded p-2 flex items-center space-x-2 hover:bg-gray-700 transition-colors cursor-pointer">
              <div class="w-8 h-8 bg-green-600 rounded flex items-center justify-center">
                <i class="fa-solid fa-music text-white text-xs"></i>
              </div>
              <span class="text-white text-xs">背景音乐.mp3</span>
            </div>
            <div class="media-item bg-gray-800 rounded p-2 flex items-center space-x-2 hover:bg-gray-700 transition-colors cursor-pointer">
              <div class="w-8 h-8 bg-purple-600 rounded flex items-center justify-center">
                <i class="fa-solid fa-image text-white text-xs"></i>
              </div>
              <span class="text-white text-xs">Logo.png</span>
            </div>
          </div>
        </div>

        <!-- AI工具 -->
        <div class="panel-section p-4 border-t border-gray-700">
          <h3 class="text-white text-sm font-medium mb-3">AI工具</h3>
          <div class="space-y-2">
            <button class="ai-tool-btn w-full bg-[var(--primary)] hover:bg-[var(--primary-dark)] text-white text-xs py-2 px-3 rounded transition-colors">
              <i class="fa-solid fa-scissors mr-2"></i>
              智能剪辑
            </button>
            <button class="ai-tool-btn w-full bg-gray-700 hover:bg-gray-600 text-white text-xs py-2 px-3 rounded transition-colors">
              <i class="fa-solid fa-eye mr-2"></i>
              场景识别
            </button>
            <button class="ai-tool-btn w-full bg-gray-700 hover:bg-gray-600 text-white text-xs py-2 px-3 rounded transition-colors">
              <i class="fa-solid fa-microphone mr-2"></i>
              语音转文字
            </button>
          </div>
        </div>
      </div>

      <!-- 中央预览区 -->
      <div class="center-panel flex-1 flex flex-col">
        <!-- 视频预览 -->
        <div class="video-preview flex-1 bg-black flex items-center justify-center relative">
          <!-- 模拟视频内容 -->
          <div class="video-content relative w-full max-w-2xl aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg overflow-hidden">
            <!-- 视频画面 -->
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="text-center text-white">
                <div class="w-16 h-16 mx-auto mb-4 bg-white/10 rounded-full flex items-center justify-center">
                  <i class="fa-solid fa-play text-2xl"></i>
                </div>
                <p class="text-lg font-medium">AI正在智能剪辑中...</p>
                <div class="mt-2 w-32 h-1 bg-gray-700 rounded-full mx-auto overflow-hidden">
                  <div class="h-full bg-[var(--primary)] rounded-full animate-pulse" style="width: 65%"></div>
                </div>
              </div>
            </div>

            <!-- 时间码 -->
            <div class="absolute top-4 left-4 bg-black/70 rounded px-2 py-1 text-white text-xs font-mono">
              00:01:23
            </div>

            <!-- AI标识 -->
            <div class="absolute top-4 right-4 bg-[var(--primary)]/90 rounded px-2 py-1 text-white text-xs">
              <i class="fa-solid fa-magic-wand-sparkles mr-1"></i>
              AI处理
            </div>
          </div>
        </div>

        <!-- 时间轴 -->
        <div class="timeline bg-[#2a2a2a] p-4 border-t border-gray-700">
          <div class="timeline-tracks space-y-2">
            <!-- 视频轨道 -->
            <div class="track">
              <div class="track-header flex items-center mb-2">
                <span class="text-white text-xs font-medium w-16">视频</span>
                <div class="flex-1 h-8 bg-gray-800 rounded relative overflow-hidden">
                  <!-- 视频片段 -->
                  <div class="absolute left-2 top-1 bottom-1 w-20 bg-blue-600 rounded flex items-center justify-center">
                    <span class="text-white text-xs">片段1</span>
                  </div>
                  <div class="absolute left-24 top-1 bottom-1 w-16 bg-blue-500 rounded flex items-center justify-center">
                    <span class="text-white text-xs">片段2</span>
                  </div>
                  <!-- 播放头 -->
                  <div class="absolute top-0 bottom-0 w-0.5 bg-red-500" style="left: 35%"></div>
                </div>
              </div>
            </div>

            <!-- 音频轨道 -->
            <div class="track">
              <div class="track-header flex items-center">
                <span class="text-white text-xs font-medium w-16">音频</span>
                <div class="flex-1 h-6 bg-gray-800 rounded relative overflow-hidden">
                  <!-- 音频波形 -->
                  <div class="absolute left-2 top-1 bottom-1 w-32 bg-green-600 rounded opacity-70"></div>
                  <div class="absolute top-0 bottom-0 w-0.5 bg-red-500" style="left: 35%"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="right-panel w-64 bg-[#1e1e1e] border-l border-gray-700">
        <div class="panel-section p-4">
          <h3 class="text-white text-sm font-medium mb-3">属性</h3>
          <div class="space-y-3">
            <div>
              <label class="text-gray-400 text-xs">位置</label>
              <div class="flex space-x-2 mt-1">
                <input type="text" value="0" class="w-full bg-gray-800 text-white text-xs p-1 rounded">
                <input type="text" value="0" class="w-full bg-gray-800 text-white text-xs p-1 rounded">
              </div>
            </div>
            <div>
              <label class="text-gray-400 text-xs">缩放</label>
              <input type="range" min="0" max="200" value="100" class="w-full mt-1">
            </div>
            <div>
              <label class="text-gray-400 text-xs">透明度</label>
              <input type="range" min="0" max="100" value="100" class="w-full mt-1">
            </div>
          </div>
        </div>

        <!-- AI建议 -->
        <div class="panel-section p-4 border-t border-gray-700">
          <h3 class="text-white text-sm font-medium mb-3">AI建议</h3>
          <div class="space-y-2">
            <div class="ai-suggestion bg-[var(--primary)]/20 border border-[var(--primary)]/30 rounded p-2">
              <p class="text-[var(--primary)] text-xs">建议在1:23处添加转场效果</p>
            </div>
            <div class="ai-suggestion bg-blue-500/20 border border-blue-500/30 rounded p-2">
              <p class="text-blue-400 text-xs">检测到音频质量可以优化</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这个组件主要用于展示，不需要复杂的逻辑
</script>

<style scoped>
.track-header {
  font-family: 'Monaco', 'Menlo', monospace;
}

.ai-tool-btn:hover {
  transform: translateY(-1px);
}

.media-item:hover {
  transform: translateX(2px);
}

.ai-suggestion {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}
</style>
