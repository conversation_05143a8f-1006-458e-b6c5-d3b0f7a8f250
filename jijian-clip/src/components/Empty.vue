<template>
  <div class="empty-state text-center py-12">
    <div class="empty-icon w-24 h-24 mx-auto mb-6 text-gray-300">
      <i class="fa-solid fa-folder-open text-6xl"></i>
    </div>
    <h3 class="text-xl font-medium text-[var(--text-dark)] mb-2">暂无项目</h3>
    <p class="text-[var(--text-light)] mb-6">开始创建您的第一个视频项目</p>
    <button class="px-6 py-3 bg-gradient-primary text-white rounded-lg hover:shadow-lg transition-all transform hover:-translate-y-1">
      <i class="fa-solid fa-plus mr-2"></i>
      创建项目
    </button>
  </div>
</template>

<script setup lang="ts">
// Empty state component
</script>

<style scoped>
.empty-icon {
  opacity: 0.6;
}

.empty-state button:hover {
  box-shadow: 0 6px 20px rgba(123, 97, 255, 0.4);
}
</style>
