<template>
  <header
    :class="[
      'header fixed top-0 left-0 right-0 z-50 transition-all duration-300',
      scrolled ? 'bg-white/95 backdrop-blur-sm shadow-md py-2' : 'bg-transparent py-4'
    ]"
  >
    <div class="container mx-auto px-4 flex items-center justify-between">
      <!-- Logo区域 - 完全复制源项目样式 -->
      <div
        class="logo text-2xl font-bold"
        v-motion
        :initial="{ opacity: 0, x: -20 }"
        :enter="{ opacity: 1, x: 0, transition: { duration: 500 } }"
      >
        <span class="text-gradient">即剪AI</span>
      </div>

      <!-- Desktop Navigation - 完全复制源项目结构 -->
      <div class="hidden md:flex items-center space-x-8">
        <nav class="flex space-x-6">
          <router-link
            to="/"
            :class="[
              'transition-colors font-medium',
              $route.path === '/'
                ? 'text-[var(--primary)]'
                : 'text-[var(--text-dark)] hover:text-[var(--primary)]'
            ]"
          >
            {{ $t('header.home') }}
          </router-link>
          <router-link
            to="/features"
            class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"
          >
            {{ $t('header.features') }}
          </router-link>
          <router-link
            to="/pricing"
            class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"
          >
            {{ $t('header.pricing') }}
          </router-link>
          <router-link
            to="/tutorials"
            class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"
          >
            {{ $t('header.tutorials') }}
          </router-link>
        </nav>

        <!-- 功能按钮组 -->
        <div class="flex items-center space-x-3">
          <button
            class="px-4 py-2 bg-gradient-primary text-white rounded-lg hover:shadow-lg transition-all transform hover:-translate-y-0.5 text-sm font-medium"
            v-motion
            :whileHover="{ scale: 1.03 }"
            :whileTap="{ scale: 0.98 }"
            @click="$emit('long-to-short-click')"
          >
            {{ $t('header.longToShort') }}
          </button>
          <button
            class="px-4 py-2 bg-gray-100 text-[var(--text-dark)] rounded-lg hover:shadow-md transition-all transform hover:-translate-y-0.5 text-sm font-medium relative"
            v-motion
            :whileHover="{ scale: 1.03 }"
            :whileTap="{ scale: 0.98 }"
          >
            {{ $t('header.mixedCut') }}
            <span class="absolute -top-1 right-0 bg-gray-400 text-white text-xs px-2 py-0.5 rounded-full whitespace-nowrap">
              {{ $t('header.comingSoon') }}
            </span>
          </button>
        </div>

        <!-- 语言切换按钮 -->
        <div class="flex items-center ml-4">
          <button
            class="px-3 py-2 rounded-lg text-[var(--text-dark)] hover:bg-gray-100 transition-colors flex items-center"
            @click="toggleLanguage"
            v-motion
            :whileHover="{ scale: 1.05 }"
            :whileTap="{ scale: 0.95 }"
          >
            {{ currentLanguage === 'zh' ? '中文' : 'English' }}
            <i class="fa-solid fa-globe ml-2"></i>
          </button>
        </div>

        <!-- 用户状态区域 -->
        <div class="flex items-center space-x-4">
          <!-- 积分显示（已登录时） -->
          <div v-if="isAuthenticated" class="hidden sm:block text-sm text-[var(--text-light)]">
            <span class="text-[var(--primary)] font-medium">234</span> credits
          </div>

          <!-- 登录/用户按钮 -->
          <button
            class="px-5 py-2 bg-gradient-primary text-white rounded-lg hover:shadow-lg transition-all transform hover:-translate-y-0.5"
            v-motion
            :whileHover="{ scale: 1.03 }"
            :whileTap="{ scale: 0.98 }"
            @click="$emit('login-click')"
          >
            {{ isAuthenticated ? $t('header.dashboard') : $t('header.login') }}
          </button>
        </div>
      </div>

      <!-- Mobile Menu Button -->
      <button
        class="md:hidden text-[var(--text-dark)] p-2 rounded-full hover:bg-gray-100 transition-colors"
        @click="mobileMenuOpen = !mobileMenuOpen"
        v-motion
        :whileHover="{ scale: 1.1 }"
        :whileTap="{ scale: 0.9 }"
      >
        <i class="fa-solid fa-bars text-xl"></i>
      </button>
    </div>

    <!-- Mobile Menu - 完全复制源项目结构 -->
    <div
      v-motion
      :initial="{ height: 0, opacity: 0, overflow: 'hidden' }"
      :enter="{
        height: mobileMenuOpen ? 'auto' : 0,
        opacity: mobileMenuOpen ? 1 : 0,
        overflow: mobileMenuOpen ? 'visible' : 'hidden',
        transition: { duration: 300, ease: [0.25, 0.1, 0.25, 1.0] }
      }"
      class="md:hidden bg-white border-t border-gray-100 px-4 py-3 shadow-lg"
    >
      <nav class="flex flex-col space-y-4 py-3">
        <router-link
          to="/"
          :class="[
            'py-2 border-b border-gray-100 transition-colors',
            $route.path === '/'
              ? 'text-[var(--primary)] font-medium'
              : 'text-[var(--text-dark)]'
          ]"
        >
          {{ $t('header.home') }}
        </router-link>
        <router-link
          to="/features"
          class="text-[var(--text-light)] py-2 border-b border-gray-100 hover:text-[var(--primary)] transition-colors"
        >
          {{ $t('header.features') }}
        </router-link>
        <router-link
          to="/pricing"
          class="text-[var(--text-light)] py-2 border-b border-gray-100 hover:text-[var(--primary)] transition-colors"
        >
          {{ $t('header.pricing') }}
        </router-link>
        <router-link
          to="/tutorials"
          class="text-[var(--text-light)] py-2 border-b border-gray-100 hover:text-[var(--primary)] transition-colors"
        >
          {{ $t('header.tutorials') }}
        </router-link>

        <!-- 移动端功能按钮 -->
        <button class="w-full py-3 bg-gradient-primary text-white rounded-lg hover:shadow-lg transition-all mb-2">
          {{ $t('header.longToShort') }}
        </button>
        <button class="w-full py-3 bg-gray-100 text-[var(--text-dark)] rounded-lg hover:shadow-md transition-all mb-2 relative">
          {{ $t('header.mixedCut') }}
          <span class="absolute top-2 right-4 bg-gray-400 text-white text-xs px-2 py-0.5 rounded-full whitespace-nowrap">
            {{ $t('header.comingSoon') }}
          </span>
        </button>

        <!-- 移动端语言切换 -->
        <button
          class="w-full py-3 border border-gray-200 rounded-lg text-[var(--text-dark)] hover:bg-gray-50 transition-colors flex items-center justify-center mb-2"
          @click="toggleLanguage"
        >
          {{ currentLanguage === 'zh' ? '切换到英文' : '切换到中文' }}
          <i class="fa-solid fa-globe ml-2"></i>
        </button>

        <!-- 移动端积分显示 -->
        <div v-if="isAuthenticated" class="text-sm text-[var(--text-light)] py-2 border-b border-gray-100">
          <span class="text-[var(--primary)] font-medium">234</span> credits
        </div>

        <!-- 移动端登录按钮 -->
        <button
          class="w-full py-3 bg-gradient-primary text-white rounded-lg hover:shadow-lg transition-all"
          @click="$emit('login-click')"
        >
          {{ isAuthenticated ? $t('header.dashboard') : $t('header.login') }}
        </button>
      </nav>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { switchLanguage, getCurrentLanguage } from '@/i18n'

// Props - 完全复制源项目接口
interface Props {
  isAuthenticated?: boolean
  userName?: string
  userAvatar?: string
}

const props = withDefaults(defineProps<Props>(), {
  isAuthenticated: false,
  userName: 'User',
  userAvatar: ''
})

// Emits - 完全复制源项目事件
const emit = defineEmits<{
  'login-click': []
  'long-to-short-click': []
  'mixed-cut-click': []
}>()

// 响应式数据
const scrolled = ref(false)
const mobileMenuOpen = ref(false)
const { locale, t } = useI18n()
const currentLanguage = ref(getCurrentLanguage())
const $route = useRoute()

// 滚动监听 - 完全复制源项目逻辑
const handleScroll = () => {
  scrolled.value = window.scrollY > 20
}

// 语言切换 - 完全复制源项目逻辑
const toggleLanguage = () => {
  const newLanguage = currentLanguage.value === 'zh' ? 'en' : 'zh'
  switchLanguage(newLanguage)
  currentLanguage.value = newLanguage
  locale.value = newLanguage
  localStorage.setItem('language', newLanguage)
}

// 生命周期
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.jijian-header {
  backdrop-filter: blur(8px);
}

.nav-link {
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-link:hover {
  transform: translateY(-1px);
}

.user-info:hover {
  background-color: var(--bg-light);
  border-radius: 8px;
  padding: 4px 8px;
  margin: -4px -8px;
}

.mobile-menu {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
