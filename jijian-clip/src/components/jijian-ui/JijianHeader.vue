<template>
  <header 
    :class="[
      'jijian-header fixed top-0 left-0 right-0 z-50 transition-all duration-300',
      scrolled ? 'bg-white/95 backdrop-blur-sm shadow-md py-2' : 'bg-transparent py-4'
    ]"
  >
    <div class="container mx-auto px-4 flex items-center justify-between">
      <!-- Logo区域 -->
      <div class="logo-section flex items-center space-x-3">
        <div class="logo text-2xl font-bold">
          <span class="jijian-brand">即剪AI</span>
        </div>
      </div>

      <!-- 导航菜单 -->
      <nav class="nav-menu hidden md:flex items-center space-x-8">
        <a 
          href="#features" 
          class="nav-link text-jijian-text-dark hover:text-jijian-primary transition-colors duration-300"
        >
          {{ $t('navigation.features') }}
        </a>
        <a 
          href="#demo" 
          class="nav-link text-jijian-text-dark hover:text-jijian-primary transition-colors duration-300"
        >
          {{ $t('navigation.demo') }}
        </a>
        <a 
          href="#projects" 
          class="nav-link text-jijian-text-dark hover:text-jijian-primary transition-colors duration-300"
        >
          {{ $t('navigation.projects') }}
        </a>
      </nav>

      <!-- 右侧操作区域 -->
      <div class="header-actions flex items-center space-x-4">
        <!-- 语言切换 -->
        <el-dropdown @command="handleLanguageChange" class="language-switcher">
          <span class="cursor-pointer text-jijian-text-light hover:text-jijian-primary transition-colors">
            {{ currentLanguage === 'zh' ? '中文' : 'EN' }}
            <el-icon class="ml-1"><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="zh" :class="{ 'text-jijian-primary': currentLanguage === 'zh' }">
                中文
              </el-dropdown-item>
              <el-dropdown-item command="en" :class="{ 'text-jijian-primary': currentLanguage === 'en' }">
                English
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 未登录状态 -->
        <template v-if="!isAuthenticated">
          <el-button 
            class="login-btn" 
            size="large" 
            @click="$emit('login-click')"
          >
            {{ $t('navigation.login') }}
          </el-button>
          <el-button 
            type="primary" 
            class="jijian-btn jijian-btn-primary" 
            size="large" 
            @click="$emit('register-click')"
          >
            {{ $t('navigation.register') }}
          </el-button>
        </template>

        <!-- 已登录状态 -->
        <template v-else>
          <el-dropdown @command="handleUserCommand">
            <div class="user-info flex items-center space-x-2 cursor-pointer">
              <el-avatar :size="32" :src="userAvatar">
                {{ userName.charAt(0) }}
              </el-avatar>
              <span class="user-name text-jijian-text-dark">{{ userName }}</span>
              <el-icon class="dropdown-icon text-jijian-text-light"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">{{ $t('navigation.profile') }}</el-dropdown-item>
                <el-dropdown-item command="settings">{{ $t('navigation.settings') }}</el-dropdown-item>
                <el-dropdown-item divided command="logout">{{ $t('navigation.logout') }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          
          <el-button 
            class="upload-btn jijian-btn" 
            size="large" 
            @click="$emit('upload-click')"
          >
            <el-icon class="mr-2"><Upload /></el-icon>
            {{ $t('navigation.upload') }}
          </el-button>
        </template>

        <!-- 移动端菜单按钮 -->
        <el-button 
          class="mobile-menu-btn md:hidden" 
          @click="mobileMenuOpen = !mobileMenuOpen"
          :icon="mobileMenuOpen ? 'Close' : 'Menu'"
          text
        />
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div 
      v-show="mobileMenuOpen"
      class="mobile-menu md:hidden bg-white border-t border-gray-100 px-4 py-4"
    >
      <nav class="flex flex-col space-y-4">
        <a href="#features" class="nav-link">{{ $t('navigation.features') }}</a>
        <a href="#demo" class="nav-link">{{ $t('navigation.demo') }}</a>
        <a href="#projects" class="nav-link">{{ $t('navigation.projects') }}</a>
      </nav>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { switchLanguage, getCurrentLanguage } from '@/i18n'
import { ArrowDown, Upload, Menu, Close } from '@element-plus/icons-vue'

// Props
interface Props {
  isAuthenticated?: boolean
  userName?: string
  userAvatar?: string
}

const props = withDefaults(defineProps<Props>(), {
  isAuthenticated: false,
  userName: 'User',
  userAvatar: ''
})

// Emits
const emit = defineEmits<{
  'login-click': []
  'register-click': []
  'upload-click': []
  'user-command': [command: string]
}>()

// 响应式数据
const scrolled = ref(false)
const mobileMenuOpen = ref(false)
const { locale } = useI18n()
const currentLanguage = ref(getCurrentLanguage())

// 滚动监听
const handleScroll = () => {
  scrolled.value = window.scrollY > 20
}

// 语言切换
const handleLanguageChange = (command: string) => {
  switchLanguage(command)
  currentLanguage.value = command
  locale.value = command
}

// 用户操作
const handleUserCommand = (command: string) => {
  emit('user-command', command)
}

// 生命周期
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.jijian-header {
  backdrop-filter: blur(8px);
}

.nav-link {
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-link:hover {
  transform: translateY(-1px);
}

.user-info:hover {
  background-color: var(--bg-light);
  border-radius: 8px;
  padding: 4px 8px;
  margin: -4px -8px;
}

.mobile-menu {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
