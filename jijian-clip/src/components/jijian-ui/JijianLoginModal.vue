<template>
  <el-dialog
    v-model="visible"
    :title="isLogin ? $t('auth.loginTitle') : $t('auth.registerTitle')"
    width="400px"
    :before-close="handleClose"
    class="jijian-login-modal"
  >
    <div class="login-content">
      <!-- Logo区域 -->
      <div class="text-center mb-6">
        <div class="jijian-brand text-3xl mb-2">即剪AI</div>
        <p class="text-[var(--text-light)]">
          {{ isLogin ? '欢迎回来' : '开始您的创作之旅' }}
        </p>
      </div>

      <!-- 登录/注册表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-position="top"
        class="login-form"
      >
        <el-form-item :label="$t('auth.email')" prop="email">
          <el-input
            v-model="formData.email"
            type="email"
            :placeholder="$t('auth.email')"
            size="large"
            class="jijian-input"
          >
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item :label="$t('auth.password')" prop="password">
          <el-input
            v-model="formData.password"
            type="password"
            :placeholder="$t('auth.password')"
            size="large"
            show-password
            class="jijian-input"
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item 
          v-if="!isLogin" 
          :label="$t('auth.confirmPassword')" 
          prop="confirmPassword"
        >
          <el-input
            v-model="formData.confirmPassword"
            type="password"
            :placeholder="$t('auth.confirmPassword')"
            size="large"
            show-password
            class="jijian-input"
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <!-- 记住我 / 忘记密码 -->
        <div v-if="isLogin" class="flex justify-between items-center mb-6">
          <el-checkbox v-model="formData.rememberMe">
            {{ $t('auth.rememberMe') }}
          </el-checkbox>
          <el-link type="primary" class="text-[var(--primary)]">
            {{ $t('auth.forgotPassword') }}
          </el-link>
        </div>

        <!-- 提交按钮 -->
        <el-button
          type="primary"
          size="large"
          class="w-full jijian-btn jijian-btn-primary mb-4"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ isLogin ? $t('auth.signIn') : $t('auth.signUp') }}
        </el-button>

        <!-- 切换登录/注册 -->
        <div class="text-center">
          <span class="text-[var(--text-light)]">
            {{ isLogin ? $t('auth.noAccount') : $t('auth.hasAccount') }}
          </span>
          <el-link
            type="primary"
            class="ml-2 text-[var(--primary)]"
            @click="toggleMode"
          >
            {{ isLogin ? $t('auth.signUp') : $t('auth.signIn') }}
          </el-link>
        </div>
      </el-form>

      <!-- 社交登录 -->
      <div class="social-login mt-6">
        <el-divider>
          <span class="text-[var(--text-light)] text-sm">或使用以下方式登录</span>
        </el-divider>
        
        <div class="flex justify-center space-x-4">
          <el-button circle size="large" class="social-btn">
            <el-icon><Platform /></el-icon>
          </el-button>
          <el-button circle size="large" class="social-btn">
            <el-icon><ChatDotRound /></el-icon>
          </el-button>
          <el-button circle size="large" class="social-btn">
            <el-icon><Platform /></el-icon>
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Message, Lock, Platform, ChatDotRound } from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue: boolean
  mode?: 'login' | 'register'
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  mode: 'login'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'login-success': [data: any]
  'register-success': [data: any]
}>()

// 响应式数据
const { t } = useI18n()
const formRef = ref<FormInstance>()
const loading = ref(false)
const isLogin = ref(props.mode === 'login')

const formData = reactive({
  email: '',
  password: '',
  confirmPassword: '',
  rememberMe: false
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单验证规则
const formRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== formData.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 方法
const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    email: '',
    password: '',
    confirmPassword: '',
    rememberMe: false
  })
}

const toggleMode = () => {
  isLogin.value = !isLogin.value
  resetForm()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (isLogin.value) {
      // 登录逻辑
      const loginData = {
        email: formData.email,
        token: 'mock_token_' + Date.now(),
        user: {
          id: 'user_' + Date.now(),
          email: formData.email,
          name: formData.email.split('@')[0]
        }
      }
      emit('login-success', loginData)
      ElMessage.success('登录成功！')
    } else {
      // 注册逻辑
      const registerData = {
        email: formData.email,
        user: {
          id: 'user_' + Date.now(),
          email: formData.email,
          name: formData.email.split('@')[0]
        }
      }
      emit('register-success', registerData)
      ElMessage.success('注册成功！')
    }

    visible.value = false
    resetForm()
  } catch (error) {
    console.error('Form validation failed:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.jijian-login-modal :deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
}

.jijian-login-modal :deep(.el-dialog__header) {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  padding: 20px 24px;
}

.jijian-login-modal :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

.login-content {
  padding: 24px;
}

.jijian-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.jijian-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 12px rgba(123, 97, 255, 0.2);
}

.social-btn {
  border: 1px solid var(--bg-light);
  color: var(--text-light);
  transition: all 0.3s ease;
}

.social-btn:hover {
  border-color: var(--primary);
  color: var(--primary);
  transform: translateY(-2px);
}
</style>
