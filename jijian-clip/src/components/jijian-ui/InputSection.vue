<template>
  <section 
    class="input-section py-16 md:py-24 flex flex-col items-center justify-center"
    v-motion
    :initial="{ opacity: 0, y: 20 }"
    :enter="{ opacity: 1, y: 0, transition: { duration: 800 } }"
  >
    <div class="w-full max-w-4xl px-4">
      <h2 class="text-2xl md:text-3xl font-bold text-center text-[var(--text-dark)] mb-8">
        描述你的视频创意，AI 即刻生成
      </h2>
      
      <textarea 
        class="prompt-input w-full h-40 md:h-48 p-4 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary)] resize-none transition-all"
        placeholder="描述你的视频创意..."
        v-model="promptText"
        readonly
        v-motion
        :initial="{ opacity: 0, scale: 0.98 }"
        :enter="{ opacity: 1, scale: 1, transition: { duration: 500, delay: 200 } }"
      ></textarea>
      
      <div class="mt-2 flex justify-between items-center">
        <div class="flex items-center">
          <div class="w-32 h-1 bg-gray-200 rounded-full overflow-hidden mr-2">
            <div 
              class="input-progress h-full bg-[var(--primary)] transition-all"
              :style="{ width: progressWidth }"
            ></div>
          </div>
          <span class="char-counter text-sm text-[var(--text-light)]">
            {{ charCount }}/1000
          </span>
        </div>
      </div>
      
      <div class="input-actions flex flex-wrap gap-3 mt-4">
        <button 
          class="action-btn url-btn px-4 py-2 border border-gray-200 rounded-lg text-[var(--text-dark)] hover:bg-gray-50 transition-colors flex items-center cursor-not-allowed"
          v-motion
          :initial="{ opacity: 0, x: -10 }"
          :enter="{ opacity: 1, x: 0, transition: { duration: 300, delay: 400 } }"
          disabled
        >
          <i class="fa-solid fa-link mr-2"></i> Add URL
        </button>
        <button 
          class="action-btn upload-btn px-4 py-2 border border-gray-200 rounded-lg text-[var(--text-dark)] hover:bg-gray-50 transition-colors flex items-center cursor-not-allowed"
          v-motion
          :initial="{ opacity: 0, x: -10 }"
          :enter="{ opacity: 1, x: 0, transition: { duration: 300, delay: 500 } }"
          disabled
        >
          <i class="fa-solid fa-upload mr-2"></i> Upload
        </button>
        <button 
          class="generate-btn ml-auto px-6 py-2 bg-[var(--primary)] text-white rounded-lg hover:bg-[var(--primary-dark)] transition-all flex items-center font-medium cursor-not-allowed"
          v-motion
          :initial="{ opacity: 0, x: 10 }"
          :enter="{ opacity: 1, x: 0, transition: { duration: 300, delay: 600 } }"
          disabled
        >
          <i class="fa-solid fa-play mr-2"></i> 演示生成过程
        </button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const promptText = ref("制作一个3分钟的产品介绍视频，突出产品特点和使用场景，风格要现代简洁...")

// 计算属性
const charCount = computed(() => promptText.value.length)
const progressWidth = computed(() => `${(charCount.value / 1000) * 100}%`)

// 生命周期
onMounted(() => {
  // 模拟打字效果
  const fullText = "制作一个3分钟的产品介绍视频，突出产品特点和使用场景，风格要现代简洁，配色以蓝白为主，包含产品展示、功能介绍、用户评价等内容，最后加上联系方式和品牌logo。视频需要有吸引人的开头，清晰的结构，以及有说服力的结尾。请确保视频节奏适中，信息传达准确，符合现代商务风格。"
  
  let currentIndex = 0
  promptText.value = ""
  
  const typeInterval = setInterval(() => {
    if (currentIndex < fullText.length) {
      promptText.value += fullText[currentIndex]
      currentIndex++
    } else {
      clearInterval(typeInterval)
    }
  }, 50)
})
</script>

<style scoped>
.prompt-input {
  font-family: 'Inter', -apple-system, sans-serif;
  line-height: 1.6;
}

.prompt-input:focus {
  box-shadow: 0 0 0 3px rgba(123, 97, 255, 0.1);
}

.input-progress {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.generate-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(123, 97, 255, 0.4);
}
</style>
