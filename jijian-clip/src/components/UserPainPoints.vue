<template>
  <div
    class="bg-white rounded-2xl shadow-xl p-8 h-full relative overflow-hidden"
    v-motion
    :initial="{ opacity: 0, y: 20 }"
    :whileInView="{ opacity: 1, y: 0 }"
    :viewport="{ once: true }"
    :transition="{ duration: 600 }"
  >
    <!-- Decorative elements - 完全复制源项目 -->
    <div class="absolute -top-20 -right-20 w-64 h-64 bg-[var(--primary)]/5 rounded-full blur-3xl"></div>

    <h2 class="text-2xl font-bold text-center text-[var(--text-dark)] mb-8 relative z-10">
      {{ $t('painPoints.title') }}
    </h2>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 relative z-10">
      <div
        v-for="(point, index) in painPointsData.points"
        :key="point.id"
        class="border border-gray-100 rounded-xl bg-white p-6 flex flex-col items-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group"
        v-motion
        :initial="{ opacity: 0, y: 30, scale: 0.95 }"
        :whileInView="{
          opacity: 1,
          y: 0,
          scale: 1,
          transition: {
            delay: 0.2 * index,
            duration: 600,
            type: 'spring',
            stiffness: 200,
            damping: 20
          }
        }"
        :viewport="{ once: true }"
      >
        <div class="w-16 h-full rounded-full bg-gradient-primary/10 flex items-center justify-center mb-4 group-hover:bg-gradient-primary/20 transition-colors duration-300">
          <span class="text-[var(--primary)] text-2xl">{{ point.icon }}</span>
        </div>
        <h3 class="font-bold text-[var(--text-dark)] mb-3 text-center">
          {{ $t(`painPoints.${point.title.toLowerCase()}`) }}
        </h3>
        <p class="text-sm text-[var(--text-light)] text-center">
          {{ $t(`painPoints.${point.title.toLowerCase()}Desc`) }}
          {{ point.description }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { painPointsData } from '@/mock/data'

const { t } = useI18n()
</script>

<style scoped>
/* 完全复制源项目样式 */
.group:hover .w-16 {
  transform: scale(1.1);
}
</style>
