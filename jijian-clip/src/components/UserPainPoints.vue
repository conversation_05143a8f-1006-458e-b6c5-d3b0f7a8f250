<template>
  <div class="bg-white rounded-2xl shadow-xl p-8 h-full relative overflow-hidden">
    <!-- Decorative elements -->
    <div class="absolute -top-20 -right-20 w-64 h-64 bg-[var(--primary)]/5 rounded-full blur-3xl"></div>

    <h2 class="text-2xl font-bold text-center text-[var(--text-dark)] mb-8 relative z-10">
      用户痛点：视频创作的三大困境
    </h2>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 relative z-10">
      <div class="border border-gray-100 rounded-xl bg-white p-6 flex flex-col items-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
        <div class="w-16 h-16 rounded-full bg-gradient-primary/10 flex items-center justify-center mb-4 group-hover:bg-gradient-primary/20 transition-colors duration-300">
          <span class="text-[var(--primary)] text-2xl">⏱️</span>
        </div>
        <h3 class="font-bold text-[var(--text-dark)] mb-3 text-center">效率瓶颈</h3>
        <p class="text-sm text-[var(--text-light)] text-center">
          传统剪辑软件操作复杂，大量时间花费在机械操作上，创意被技术实现所拖累
        </p>
      </div>

      <div class="border border-gray-100 rounded-xl bg-white p-6 flex flex-col items-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
        <div class="w-16 h-16 rounded-full bg-gradient-primary/10 flex items-center justify-center mb-4 group-hover:bg-gradient-primary/20 transition-colors duration-300">
          <span class="text-[var(--primary)] text-2xl">📚</span>
        </div>
        <h3 class="font-bold text-[var(--text-dark)] mb-3 text-center">学习困难</h3>
        <p class="text-sm text-[var(--text-light)] text-center">
          专业软件学习曲线陡峭，需要掌握大量专业知识和操作技巧才能上手创作
        </p>
      </div>

      <div class="border border-gray-100 rounded-xl bg-white p-6 flex flex-col items-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
        <div class="w-16 h-16 rounded-full bg-gradient-primary/10 flex items-center justify-center mb-4 group-hover:bg-gradient-primary/20 transition-colors duration-300">
          <span class="text-[var(--primary)] text-2xl">🤝</span>
        </div>
        <h3 class="font-bold text-[var(--text-dark)] mb-3 text-center">协作障碍</h3>
        <p class="text-sm text-[var(--text-light)] text-center">
          多人协作流程繁琐，版本管理复杂，反馈沟通成本高，创意难以高效整合
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 用户痛点组件 - 完全复制源项目内容
</script>

<style scoped>
.group:hover .w-16 {
  transform: scale(1.1);
}
</style>
