<template>
  <div class="user-pain-points jijian-card p-8">
    <h3 class="text-2xl font-bold text-jijian-text-dark mb-6">用户痛点</h3>
    <div class="space-y-6">
      <div class="pain-point-item flex items-start space-x-4">
        <div class="icon-wrapper w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
          <el-icon class="text-red-500 text-xl"><Warning /></el-icon>
        </div>
        <div>
          <h4 class="font-semibold text-jijian-text-dark mb-2">传统剪辑软件复杂难用</h4>
          <p class="text-jijian-text-light">学习成本高，操作繁琐，需要专业技能</p>
        </div>
      </div>
      
      <div class="pain-point-item flex items-start space-x-4">
        <div class="icon-wrapper w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
          <el-icon class="text-orange-500 text-xl"><Clock /></el-icon>
        </div>
        <div>
          <h4 class="font-semibold text-jijian-text-dark mb-2">制作周期长</h4>
          <p class="text-jijian-text-light">从构思到成品需要大量时间投入</p>
        </div>
      </div>
      
      <div class="pain-point-item flex items-start space-x-4">
        <div class="icon-wrapper w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
          <el-icon class="text-yellow-500 text-xl"><Money /></el-icon>
        </div>
        <div>
          <h4 class="font-semibold text-jijian-text-dark mb-2">成本高昂</h4>
          <p class="text-jijian-text-light">专业软件授权费用和人力成本</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Warning, Clock, Money } from '@element-plus/icons-vue'
</script>

<style scoped>
.pain-point-item {
  transition: all 0.3s ease;
}

.pain-point-item:hover {
  transform: translateX(8px);
}

.icon-wrapper {
  transition: all 0.3s ease;
}

.pain-point-item:hover .icon-wrapper {
  transform: scale(1.1);
}
</style>
