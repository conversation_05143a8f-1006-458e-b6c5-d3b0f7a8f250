<template>
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4">
      <FadeInSection :delay="0.1">
        <div class="text-center mb-16">
          <h2 class="font-bold text-[var(--text-dark)] mb-6" style="font-size: 3.75rem;">
            为什么选择<span class="text-gradient">即剪AI</span>？
          </h2>
          <p class="text-[var(--text-light)] max-w-3xl mx-auto" style="font-size: 1.25rem; line-height: 1.75rem;">
            革命性的AI技术，让视频创作变得简单、高效、专业
          </p>
        </div>
      </FadeInSection>

      <!-- 特色功能卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
        <FadeInSection 
          v-for="(feature, index) in features" 
          :key="feature.id"
          :delay="0.2 + index * 0.1"
        >
          <div 
            :class="[
              'rounded-xl p-8 text-center hover:transform hover:-translate-y-2 transition-all duration-300 shadow-[var(--shadow)] hover:shadow-xl',
              feature.bgClass
            ]"
          >
            <div 
              :class="[
                'w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6',
                feature.iconBgClass
              ]"
            >
              <i :class="[feature.iconClass, 'text-white text-3xl']"></i>
            </div>
            <h3 class="text-2xl font-bold text-[var(--text-dark)] mb-4">{{ feature.title }}</h3>
            <p class="text-[var(--text-light)] text-lg">{{ feature.description }}</p>
          </div>
        </FadeInSection>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import FadeInSection from './FadeInSection.vue'

interface Feature {
  id: string
  title: string
  description: string
  iconClass: string
  iconBgClass: string
  bgClass: string
}

const features = ref<Feature[]>([
  {
    id: 'ai-editing',
    title: '智能AI剪辑',
    description: '先进的AI算法自动识别精彩片段，智能剪辑生成专业视频',
    iconClass: 'fa-solid fa-robot',
    iconBgClass: 'bg-blue-500',
    bgClass: 'bg-gradient-to-br from-blue-50 to-blue-100'
  },
  {
    id: 'natural-language',
    title: '自然语言交互',
    description: '用简单的对话描述需求，AI理解并执行复杂的视频编辑任务',
    iconClass: 'fa-solid fa-comments',
    iconBgClass: 'bg-green-500',
    bgClass: 'bg-gradient-to-br from-green-50 to-green-100'
  },
  {
    id: 'fast-generation',
    title: '极速生成',
    description: '几分钟内完成传统需要数小时的视频剪辑工作',
    iconClass: 'fa-solid fa-bolt',
    iconBgClass: 'bg-[var(--primary)]',
    bgClass: 'bg-gradient-to-br from-purple-50 to-purple-100'
  }
])
</script>

<style scoped>
/* 组件特定样式 */
</style>
