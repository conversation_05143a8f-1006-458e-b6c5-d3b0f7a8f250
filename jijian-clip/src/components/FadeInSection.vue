<template>
  <div 
    ref="sectionRef"
    :class="[
      'transition-all duration-700 ease-out',
      isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
    ]"
    :style="{ transitionDelay: `${delay}s` }"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface Props {
  delay?: number
  threshold?: number
}

const props = withDefaults(defineProps<Props>(), {
  delay: 0,
  threshold: 0.1
})

const sectionRef = ref<HTMLElement>()
const isVisible = ref(false)

let observer: IntersectionObserver | null = null

onMounted(() => {
  if (sectionRef.value) {
    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            isVisible.value = true
            // 一旦可见就停止观察
            observer?.unobserve(entry.target)
          }
        })
      },
      {
        threshold: props.threshold,
        rootMargin: '50px'
      }
    )
    
    observer.observe(sectionRef.value)
  }
})

onUnmounted(() => {
  if (observer) {
    observer.disconnect()
  }
})
</script>

<style scoped>
/* 组件特定样式 */
</style>
