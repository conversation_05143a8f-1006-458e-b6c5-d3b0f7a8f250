<template>
  <!-- CTA Section - 增强版 -->
  <FadeInSection :delay="delay">
    <div class="mt-20 bg-gradient-primary rounded-2xl p-12 md:p-16 text-white text-center shadow-[var(--shadow)] relative overflow-hidden">
      <!-- 装饰性背景元素 -->
      <div class="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
      <div class="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
      
      <div class="relative z-10">
        <div class="inline-flex items-center bg-white/20 rounded-full px-6 py-2 mb-6">
          <i class="fa-solid fa-star text-yellow-300 mr-2"></i>
          <span class="text-sm font-medium">{{ badgeText }}</span>
        </div>
        
        <h2 class="font-bold mb-6" style="font-size: 3.75rem;">
          {{ title }}
        </h2>
        <p class="mb-10 max-w-3xl mx-auto text-white/90" style="font-size: 1.25rem; line-height: 1.75rem;">
          {{ description }}
        </p>
        
        <div class="flex flex-col sm:flex-row justify-center gap-4">
          <button
            class="px-8 py-4 bg-white text-[var(--primary)] rounded-lg text-lg font-semibold shadow-[var(--shadow)] hover:shadow-xl transition-all transform hover:-translate-y-1 hover:scale-105"
            @click="handlePrimaryAction"
          >
            <i :class="primaryButton.iconClass" class="mr-2"></i>
            {{ primaryButton.text }}
          </button>
          <button
            class="px-8 py-4 bg-transparent border-2 border-white text-white rounded-lg text-lg font-semibold hover:bg-white hover:text-[var(--primary)] hover:shadow-[var(--shadow)] transition-all transform hover:-translate-y-1 hover:scale-105"
            @click="handleSecondaryAction"
          >
            <i :class="secondaryButton.iconClass" class="mr-2"></i>
            {{ secondaryButton.text }}
          </button>
        </div>
        
        <div class="mt-8 text-white/70">
          <p class="text-sm">{{ features }}</p>
        </div>
      </div>
    </div>
  </FadeInSection>
</template>

<script setup lang="ts">
import FadeInSection from './FadeInSection.vue'

interface ButtonConfig {
  text: string
  iconClass: string
}

interface Props {
  delay?: number
  badgeText?: string
  title?: string
  description?: string
  primaryButton?: ButtonConfig
  secondaryButton?: ButtonConfig
  features?: string
}

const props = withDefaults(defineProps<Props>(), {
  delay: 0.5,
  badgeText: '限时免费体验',
  title: '开始你的AI视频创作之旅',
  description: '无需复杂操作，用文字释放你的创意潜能。立即体验AI驱动的视频剪辑革命。',
  primaryButton: () => ({
    text: '免费开始使用',
    iconClass: 'fa-solid fa-rocket'
  }),
  secondaryButton: () => ({
    text: '观看演示',
    iconClass: 'fa-solid fa-play'
  }),
  features: '✨ 无需信用卡 • 🚀 即刻开始 • 💎 专业品质'
})

const emit = defineEmits<{
  primaryAction: []
  secondaryAction: []
}>()

const handlePrimaryAction = () => {
  emit('primaryAction')
}

const handleSecondaryAction = () => {
  emit('secondaryAction')
}
</script>

<style scoped>
/* 组件特定样式 */
</style>
