<template>
  <!-- 完全复制源项目的ProjectsSection组件 -->
  <section class="projects-section">
    <div class="flex flex-col md:flex-row md:items-end justify-between mb-8">
      <div>
        <h2 class="text-3xl font-bold text-gray-900 mb-2">创意作品展示</h2>
        <p class="text-gray-600">探索用户使用即剪AI创作的精彩视频</p>
      </div>
      <button
        class="mt-4 md:mt-0 text-purple-600 hover:text-purple-700 transition-colors flex items-center font-medium"
        v-motion
        :whileHover="{ x: 5 }"
        :transition="{ type: 'spring', stiffness: 300, damping: 20 }"
      >
        查看全部作品 <i class="fa-solid fa-arrow-right ml-2"></i>
      </button>
    </div>

    <div class="projects-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <div
        v-if="projectsData.length > 0"
        v-for="(project, index) in projectsData"
        :key="project.id"
        class="project-card bg-white rounded-xl shadow-lg overflow-hidden hover:transform hover:-translate-y-3 transition-all duration-500 hover:shadow-xl group"
        v-motion
        :initial="{ opacity: 0, y: 40, scale: 0.95 }"
        :whileInView="{
          opacity: 1,
          y: 0,
          scale: 1,
          transition: {
            delay: 0.1 * index,
            duration: 500,
            type: 'spring',
            stiffness: 200,
            damping: 20
          }
        }"
        :viewport="{ once: true, margin: '-100px' }"
      >
        <div class="relative aspect-video bg-gray-100 overflow-hidden">
          <img
            :src="project.thumbnail"
            :alt="project.title"
            class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
          />
          <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <div
              class="w-14 h-14 rounded-full bg-white/90 flex items-center justify-center backdrop-blur-sm"
              v-motion
              :whileHover="{ scale: 1.1 }"
              :whileTap="{ scale: 0.9 }"
            >
              <i class="fa-solid fa-play text-purple-600 text-xl"></i>
            </div>
          </div>
          <div class="absolute bottom-3 right-3 bg-black/60 text-white text-xs px-3 py-1 rounded-full backdrop-blur-sm">
            {{ project.duration }}
          </div>
          <div class="absolute top-3 left-3 bg-white/90 text-purple-600 text-xs px-3 py-1 rounded-full backdrop-blur-sm font-medium">
            热门
          </div>
        </div>
        <div class="p-5">
          <h3 class="font-bold text-gray-900 mb-2 line-clamp-1 group-hover:text-purple-600 transition-colors">{{ project.title }}</h3>
          <p class="text-sm text-gray-600 mb-4 line-clamp-2">{{ project.description }}</p>
          <div class="flex justify-between items-center">
            <span class="text-xs text-gray-600">{{ project.date }}</span>
            <div class="flex space-x-3">
              <button class="text-gray-600 hover:text-purple-600 transition-colors p-1.5 rounded-full hover:bg-purple-100">
                <i class="fa-solid fa-heart"></i>
              </button>
              <button class="text-gray-600 hover:text-purple-600 transition-colors p-1.5 rounded-full hover:bg-purple-100">
                <i class="fa-solid fa-download"></i>
              </button>
              <button class="text-gray-600 hover:text-purple-600 transition-colors p-1.5 rounded-full hover:bg-purple-100">
                <i class="fa-solid fa-ellipsis-v"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="col-span-full flex justify-center items-center h-64">
        <Empty />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { projectsData } from '@/mock/data'
import Empty from '@/components/Empty.vue'

const { t } = useI18n()
</script>

<style scoped>
/* 完全复制源项目样式 */
.project-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
</style>
