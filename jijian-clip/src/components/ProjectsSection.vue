<template>
  <div class="projects-section">
    <h2 class="text-3xl font-bold text-center text-jijian-text-dark mb-12">我的项目</h2>
    
    <!-- 项目统计 -->
    <div class="stats-grid grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
      <div class="stat-card jijian-card p-6 text-center">
        <div class="stat-number text-3xl font-bold text-jijian-primary mb-2">{{ projectStats.total }}</div>
        <div class="stat-label text-jijian-text-light">总项目数</div>
      </div>
      <div class="stat-card jijian-card p-6 text-center">
        <div class="stat-number text-3xl font-bold text-green-500 mb-2">{{ projectStats.completed }}</div>
        <div class="stat-label text-jijian-text-light">已完成</div>
      </div>
      <div class="stat-card jijian-card p-6 text-center">
        <div class="stat-number text-3xl font-bold text-orange-500 mb-2">{{ projectStats.processing }}</div>
        <div class="stat-label text-jijian-text-light">处理中</div>
      </div>
      <div class="stat-card jijian-card p-6 text-center">
        <div class="stat-number text-3xl font-bold text-blue-500 mb-2">{{ projectStats.totalDuration }}</div>
        <div class="stat-label text-jijian-text-light">总时长(分钟)</div>
      </div>
    </div>

    <!-- 最近项目 -->
    <div class="recent-projects">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-jijian-text-dark">最近项目</h3>
        <el-button type="primary" class="jijian-btn jijian-btn-primary" @click="goToAllProjects">
          查看全部
        </el-button>
      </div>
      
      <div class="projects-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div 
          v-for="project in recentProjects" 
          :key="project.id"
          class="project-card jijian-card p-6 cursor-pointer hover:shadow-xl transition-all duration-300"
          @click="openProject(project.id)"
        >
          <div class="project-thumbnail relative mb-4">
            <img 
              :src="project.thumbnail" 
              :alt="project.name"
              class="w-full h-32 object-cover rounded-lg"
            />
            <div class="play-overlay absolute inset-0 bg-black/40 rounded-lg flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
              <el-icon class="text-white text-3xl"><VideoPlay /></el-icon>
            </div>
            <div class="duration-badge absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
              {{ project.duration }}
            </div>
          </div>
          
          <h4 class="font-semibold text-jijian-text-dark mb-2">{{ project.name }}</h4>
          <p class="text-jijian-text-light text-sm mb-3">{{ project.description }}</p>
          
          <div class="project-meta flex justify-between items-center text-xs text-jijian-text-light">
            <span>{{ project.createdAt }}</span>
            <span class="status-badge px-2 py-1 rounded-full" :class="getStatusClass(project.status)">
              {{ getStatusText(project.status) }}
            </span>
          </div>
        </div>
        
        <!-- 新建项目卡片 -->
        <div 
          class="new-project-card jijian-card p-6 border-2 border-dashed border-jijian-primary/30 hover:border-jijian-primary hover:bg-jijian-primary/5 cursor-pointer transition-all duration-300 flex flex-col items-center justify-center text-center"
          @click="createNewProject"
        >
          <el-icon class="text-4xl text-jijian-primary mb-4"><Plus /></el-icon>
          <h4 class="font-semibold text-jijian-text-dark mb-2">新建项目</h4>
          <p class="text-jijian-text-light text-sm">开始您的视频创作之旅</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { VideoPlay, Plus } from '@element-plus/icons-vue'

const router = useRouter()

// 项目统计数据
const projectStats = ref({
  total: 12,
  completed: 8,
  processing: 2,
  totalDuration: 156
})

// 最近项目数据
const recentProjects = ref([
  {
    id: '1',
    name: '产品介绍视频',
    description: '公司新产品的宣传视频',
    thumbnail: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Product%20introduction%20video%20thumbnail&sign=abc123',
    duration: '02:30',
    status: 'completed',
    createdAt: '2024-01-15'
  },
  {
    id: '2',
    name: '团队介绍',
    description: '团队成员介绍短片',
    thumbnail: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Team%20introduction%20video%20thumbnail&sign=def456',
    duration: '01:45',
    status: 'processing',
    createdAt: '2024-01-14'
  },
  {
    id: '3',
    name: '教程视频',
    description: '产品使用教程',
    thumbnail: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Tutorial%20video%20thumbnail&sign=ghi789',
    duration: '05:20',
    status: 'completed',
    createdAt: '2024-01-13'
  }
])

// 方法
const openProject = (projectId: string) => {
  router.push(`/clip/${projectId}`)
}

const createNewProject = () => {
  router.push('/upload')
}

const goToAllProjects = () => {
  router.push('/projects')
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-600'
    case 'processing':
      return 'bg-orange-100 text-orange-600'
    case 'draft':
      return 'bg-gray-100 text-gray-600'
    default:
      return 'bg-gray-100 text-gray-600'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed':
      return '已完成'
    case 'processing':
      return '处理中'
    case 'draft':
      return '草稿'
    default:
      return '未知'
  }
}
</script>

<style scoped>
.project-card:hover {
  transform: translateY(-4px);
}

.new-project-card:hover {
  transform: translateY(-4px);
}

.stat-card:hover {
  transform: translateY(-2px);
}

.play-overlay {
  transition: all 0.3s ease;
}

.duration-badge {
  backdrop-filter: blur(4px);
}
</style>
