<template>
  <div class="video-grid">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div 
        v-for="(video, index) in videos" 
        :key="video.id"
        class="video-card group cursor-pointer"
        @click="handleVideoClick(video)"
      >
        <!-- 视频缩略图容器 -->
        <div class="video-thumbnail-container relative aspect-video rounded-xl overflow-hidden bg-gray-900 shadow-lg group-hover:shadow-xl transition-all duration-300">
          <!-- 缩略图背景 -->
          <div class="absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-900"></div>
          
          <!-- 模拟视频缩略图 -->
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="text-center text-white">
              <div class="w-16 h-16 mx-auto mb-3 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
                <i :class="video.icon" class="text-2xl text-white/80"></i>
              </div>
              <div class="text-sm font-medium">{{ video.type }}</div>
            </div>
          </div>
          
          <!-- 播放覆盖层 -->
          <div class="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <i class="fa-solid fa-play text-white ml-0.5"></i>
            </div>
          </div>
          
          <!-- 时长标签 -->
          <div class="absolute bottom-3 right-3">
            <div class="bg-black/70 rounded px-2 py-1 text-white text-xs font-mono">
              {{ video.duration }}
            </div>
          </div>
          
          <!-- AI标签 -->
          <div class="absolute top-3 left-3">
            <div class="bg-[var(--primary)]/90 rounded-full px-2 py-1 text-white text-xs font-medium">
              <i class="fa-solid fa-magic-wand-sparkles mr-1"></i>
              AI生成
            </div>
          </div>
          
          <!-- 质量指示器 -->
          <div class="absolute top-3 right-3">
            <div class="bg-green-500/90 rounded px-2 py-1 text-white text-xs font-medium">
              {{ video.quality }}
            </div>
          </div>
        </div>
        
        <!-- 视频信息 -->
        <div class="video-info mt-4">
          <h4 class="text-lg font-semibold text-[var(--text-dark)] mb-2 group-hover:text-[var(--primary)] transition-colors">
            {{ video.title }}
          </h4>
          <p class="text-[var(--text-light)] text-sm mb-3 line-clamp-2">
            {{ video.description }}
          </p>
          
          <!-- 标签 -->
          <div class="flex flex-wrap gap-2 mb-3">
            <span 
              v-for="tag in video.tags" 
              :key="tag"
              class="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
            >
              {{ tag }}
            </span>
          </div>
          
          <!-- 统计信息 -->
          <div class="flex items-center justify-between text-xs text-[var(--text-light)]">
            <div class="flex items-center space-x-4">
              <span class="flex items-center">
                <i class="fa-solid fa-eye mr-1"></i>
                {{ video.views }}
              </span>
              <span class="flex items-center">
                <i class="fa-solid fa-heart mr-1"></i>
                {{ video.likes }}
              </span>
            </div>
            <span>{{ video.createdAt }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Video {
  id: string
  title: string
  description: string
  duration: string
  type: string
  icon: string
  quality: string
  tags: string[]
  views: string
  likes: string
  createdAt: string
}

const videos = ref<Video[]>([
  {
    id: 'demo-1',
    title: '产品宣传片自动剪辑',
    description: '使用AI技术自动识别产品特写镜头，智能剪辑生成专业宣传片',
    duration: '02:34',
    type: '产品展示',
    icon: 'fa-solid fa-box',
    quality: '4K',
    tags: ['产品', '宣传', 'AI剪辑'],
    views: '1.2k',
    likes: '89',
    createdAt: '2天前'
  },
  {
    id: 'demo-2',
    title: '会议记录智能摘要',
    description: '自动识别会议重点内容，生成精简版会议记录视频',
    duration: '01:45',
    type: '会议记录',
    icon: 'fa-solid fa-users',
    quality: 'HD',
    tags: ['会议', '摘要', '智能'],
    views: '856',
    likes: '67',
    createdAt: '3天前'
  },
  {
    id: 'demo-3',
    title: '教学视频自动分段',
    description: 'AI识别教学内容结构，自动分段并添加章节标题',
    duration: '03:12',
    type: '教育培训',
    icon: 'fa-solid fa-graduation-cap',
    quality: '4K',
    tags: ['教育', '分段', '自动'],
    views: '2.1k',
    likes: '156',
    createdAt: '1周前'
  },
  {
    id: 'demo-4',
    title: '直播精彩片段提取',
    description: '从长时间直播中自动提取精彩片段，生成集锦视频',
    duration: '04:28',
    type: '直播剪辑',
    icon: 'fa-solid fa-video',
    quality: 'HD',
    tags: ['直播', '精彩', '集锦'],
    views: '3.4k',
    likes: '234',
    createdAt: '1周前'
  },
  {
    id: 'demo-5',
    title: '采访内容智能编辑',
    description: '自动识别问答结构，智能剪辑生成流畅的采访视频',
    duration: '02:56',
    type: '采访编辑',
    icon: 'fa-solid fa-microphone',
    quality: '4K',
    tags: ['采访', '问答', '编辑'],
    views: '1.8k',
    likes: '123',
    createdAt: '2周前'
  },
  {
    id: 'demo-6',
    title: '旅行Vlog自动剪辑',
    description: 'AI识别风景和人物镜头，自动生成精美旅行视频',
    duration: '05:15',
    type: '旅行Vlog',
    icon: 'fa-solid fa-plane',
    quality: '4K',
    tags: ['旅行', 'Vlog', '风景'],
    views: '4.2k',
    likes: '312',
    createdAt: '2周前'
  }
])

const emit = defineEmits<{
  videoClick: [video: Video]
}>()

const handleVideoClick = (video: Video) => {
  emit('videoClick', video)
}
</script>

<style scoped>
.video-card {
  @apply transform hover:-translate-y-1 transition-all duration-300;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
