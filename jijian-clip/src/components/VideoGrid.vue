<template>
  <div class="video-grid">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div
        v-for="(video, index) in videos"
        :key="video.id"
        class="video-card group cursor-pointer transform hover:-translate-y-2 transition-all duration-500"
        @click="handleVideoClick(video)"
      >
        <!-- 视频缩略图容器 -->
        <div class="video-thumbnail-container relative aspect-video rounded-2xl overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-black shadow-xl group-hover:shadow-2xl transition-all duration-500">
          <!-- 动态背景渐变 -->
          <div class="absolute inset-0 bg-gradient-to-br from-purple-900/30 via-blue-900/30 to-indigo-900/30 group-hover:from-purple-800/40 group-hover:via-blue-800/40 group-hover:to-indigo-800/40 transition-all duration-500"></div>

          <!-- 模拟视频缩略图 -->
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="text-center text-white transform group-hover:scale-105 transition-transform duration-300">
              <div class="w-20 h-20 mx-auto mb-4 bg-white/10 backdrop-blur-md rounded-full flex items-center justify-center border border-white/20 group-hover:bg-white/20 transition-all duration-300">
                <i :class="video.icon" class="text-3xl text-white/90"></i>
              </div>
              <div class="text-lg font-semibold mb-2">{{ video.type }}</div>
              <div class="text-sm text-white/70">AI智能处理</div>
            </div>
          </div>

          <!-- 播放覆盖层 -->
          <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center backdrop-blur-sm">
            <div class="w-16 h-16 bg-[var(--primary)] rounded-full flex items-center justify-center shadow-lg transform scale-0 group-hover:scale-100 transition-transform duration-300">
              <i class="fa-solid fa-play text-white text-xl ml-1"></i>
            </div>
          </div>

          <!-- 时长标签 -->
          <div class="absolute bottom-4 right-4">
            <div class="bg-black/80 backdrop-blur-sm rounded-lg px-3 py-1 text-white text-sm font-mono border border-white/10">
              {{ video.duration }}
            </div>
          </div>

          <!-- AI标签 -->
          <div class="absolute top-4 left-4">
            <div class="bg-gradient-to-r from-[var(--primary)] to-purple-600 rounded-full px-3 py-1 text-white text-xs font-bold shadow-lg">
              <i class="fa-solid fa-magic-wand-sparkles mr-1"></i>
              AI
            </div>
          </div>

          <!-- 质量指示器 -->
          <div class="absolute top-4 right-4">
            <div class="bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg px-2 py-1 text-white text-xs font-bold shadow-lg">
              {{ video.quality }}
            </div>
          </div>

          <!-- 装饰性光效 -->
          <div class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
            <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
            <div class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[var(--primary)]/50 to-transparent"></div>
          </div>
        </div>
        
        <!-- 视频信息 -->
        <div class="video-info mt-6 px-2">
          <h4 class="text-xl font-bold text-[var(--text-dark)] mb-3 group-hover:text-[var(--primary)] transition-colors duration-300">
            {{ video.title }}
          </h4>
          <p class="text-[var(--text-light)] text-sm mb-4 line-clamp-2 leading-relaxed">
            {{ video.description }}
          </p>

          <!-- 标签 -->
          <div class="flex flex-wrap gap-2 mb-4">
            <span
              v-for="tag in video.tags"
              :key="tag"
              class="inline-block bg-gradient-to-r from-gray-100 to-gray-50 text-gray-700 text-xs px-3 py-1 rounded-full border border-gray-200 hover:border-[var(--primary)] hover:bg-gradient-to-r hover:from-[var(--primary)]/10 hover:to-purple-50 transition-all duration-200"
            >
              {{ tag }}
            </span>
          </div>

          <!-- 统计信息 -->
          <div class="flex items-center justify-between text-sm">
            <div class="flex items-center space-x-4 text-[var(--text-light)]">
              <span class="flex items-center hover:text-[var(--primary)] transition-colors">
                <i class="fa-solid fa-eye mr-1"></i>
                {{ video.views }}
              </span>
              <span class="flex items-center hover:text-red-500 transition-colors">
                <i class="fa-solid fa-heart mr-1"></i>
                {{ video.likes }}
              </span>
            </div>
            <span class="text-[var(--text-light)] font-medium">{{ video.createdAt }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Video {
  id: string
  title: string
  description: string
  duration: string
  type: string
  icon: string
  quality: string
  tags: string[]
  views: string
  likes: string
  createdAt: string
}

const videos = ref<Video[]>([
  {
    id: 'demo-1',
    title: '产品宣传片自动剪辑',
    description: '使用AI技术自动识别产品特写镜头，智能剪辑生成专业宣传片',
    duration: '02:34',
    type: '产品展示',
    icon: 'fa-solid fa-box',
    quality: '4K',
    tags: ['产品', '宣传', 'AI剪辑'],
    views: '1.2k',
    likes: '89',
    createdAt: '2天前'
  },
  {
    id: 'demo-2',
    title: '会议记录智能摘要',
    description: '自动识别会议重点内容，生成精简版会议记录视频',
    duration: '01:45',
    type: '会议记录',
    icon: 'fa-solid fa-users',
    quality: 'HD',
    tags: ['会议', '摘要', '智能'],
    views: '856',
    likes: '67',
    createdAt: '3天前'
  },
  {
    id: 'demo-3',
    title: '教学视频自动分段',
    description: 'AI识别教学内容结构，自动分段并添加章节标题',
    duration: '03:12',
    type: '教育培训',
    icon: 'fa-solid fa-graduation-cap',
    quality: '4K',
    tags: ['教育', '分段', '自动'],
    views: '2.1k',
    likes: '156',
    createdAt: '1周前'
  },
  {
    id: 'demo-4',
    title: '直播精彩片段提取',
    description: '从长时间直播中自动提取精彩片段，生成集锦视频',
    duration: '04:28',
    type: '直播剪辑',
    icon: 'fa-solid fa-video',
    quality: 'HD',
    tags: ['直播', '精彩', '集锦'],
    views: '3.4k',
    likes: '234',
    createdAt: '1周前'
  },
  {
    id: 'demo-5',
    title: '采访内容智能编辑',
    description: '自动识别问答结构，智能剪辑生成流畅的采访视频',
    duration: '02:56',
    type: '采访编辑',
    icon: 'fa-solid fa-microphone',
    quality: '4K',
    tags: ['采访', '问答', '编辑'],
    views: '1.8k',
    likes: '123',
    createdAt: '2周前'
  },
  {
    id: 'demo-6',
    title: '旅行Vlog自动剪辑',
    description: 'AI识别风景和人物镜头，自动生成精美旅行视频',
    duration: '05:15',
    type: '旅行Vlog',
    icon: 'fa-solid fa-plane',
    quality: '4K',
    tags: ['旅行', 'Vlog', '风景'],
    views: '4.2k',
    likes: '312',
    createdAt: '2周前'
  }
])

const emit = defineEmits<{
  videoClick: [video: Video]
}>()

const handleVideoClick = (video: Video) => {
  emit('videoClick', video)
}
</script>

<style scoped>
.video-card {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.video-card:hover {
  transform: translateY(-8px) scale(1.02);
}

.video-thumbnail-container {
  position: relative;
  overflow: hidden;
}

.video-thumbnail-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
  z-index: 10;
}

.video-card:hover .video-thumbnail-container::before {
  left: 100%;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-card {
    margin-bottom: 2rem;
  }

  .video-card:hover {
    transform: translateY(-4px) scale(1.01);
  }

  .video-info {
    margin-top: 1rem;
    padding: 0 0.5rem;
  }
}
</style>
