<template>
  <!-- 数据统计展示 -->
  <FadeInSection :delay="delay">
    <div class="bg-gradient-to-r from-[var(--primary)]/5 to-blue-500/5 rounded-xl p-12">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
        <div 
          v-for="(stat, index) in stats" 
          :key="stat.id"
          class="animate-fade-in"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <div class="text-4xl font-bold text-[var(--primary)] mb-2">{{ stat.value }}</div>
          <div class="text-[var(--text-light)]">{{ stat.label }}</div>
        </div>
      </div>
    </div>
  </FadeInSection>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import FadeInSection from './FadeInSection.vue'

interface StatItem {
  id: string
  value: string
  label: string
}

interface Props {
  delay?: number
}

const props = withDefaults(defineProps<Props>(), {
  delay: 0.5
})

const stats = ref<StatItem[]>([
  {
    id: 'users',
    value: '10万+',
    label: '用户信赖'
  },
  {
    id: 'videos',
    value: '500万+',
    label: '视频生成'
  },
  {
    id: 'time-saved',
    value: '95%',
    label: '时间节省'
  },
  {
    id: 'service',
    value: '24/7',
    label: '在线服务'
  }
])
</script>

<style scoped>
/* 组件特定样式 */
</style>
