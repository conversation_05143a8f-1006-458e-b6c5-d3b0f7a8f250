<template>
  <div class="jijian-homepage min-h-screen bg-jijian-bg-light">
    <!-- 顶部导航栏 -->
    <JijianHeader
      :is-authenticated="userStore.isLoggedIn"
      :user-name="userStore.userName"
      :user-avatar="userStore.userAvatar"
      @login-click="showLoginModal = true"
      @register-click="showRegisterModal = true"
      @upload-click="goToUpload"
      @user-command="handleUserCommand"
    />

    <!-- 登录弹窗 -->
    <JijianLoginModal
      v-model="showLoginModal"
      mode="login"
      @login-success="handleLoginSuccess"
    />

    <!-- 注册弹窗 -->
    <JijianLoginModal
      v-model="showRegisterModal"
      mode="register"
      @register-success="handleRegisterSuccess"
    />

    <!-- 主要内容区域 -->
    <main class="main-content pt-24">
      <!-- Hero Section -->
      <section class="relative overflow-hidden bg-gradient-to-b from-white to-jijian-bg-light py-16 md:py-24">
        <div class="absolute top-0 right-0 w-1/2 h-full bg-jijian-primary/5 rounded-l-full -z-10"></div>
        <div class="max-w-7xl mx-auto px-4 relative z-10">
          <div class="fade-in-section">
            <div class="text-center max-w-3xl mx-auto mb-12">
              <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                <span class="text-jijian-text-dark">用对话创造精彩视频，</span>
                <span class="text-gradient">AI驱动的视频剪辑革命</span>
              </h1>
              <p class="text-xl text-jijian-text-light mb-8">
                {{ $t('hero.subtitle') }}
              </p>
              <div class="flex flex-col sm:flex-row justify-center gap-4">
                <el-button 
                  type="primary"
                  size="large"
                  class="jijian-btn jijian-btn-primary px-8 py-4 text-lg font-medium"
                  @click="handleTryFree"
                >
                  {{ $t('hero.tryFree') }} 
                  <el-icon class="ml-2"><ArrowRight /></el-icon>
                </el-button>
                <el-button 
                  size="large"
                  class="jijian-btn px-8 py-4 text-lg font-medium bg-white text-jijian-text-dark shadow-md hover:shadow-lg"
                  @click="handleWatchDemo"
                >
                  <el-icon class="mr-2 text-jijian-primary"><VideoPlay /></el-icon> 
                  {{ $t('hero.watchDemo') }}
                </el-button>
              </div>
            </div>
          </div>
          
          <div class="fade-in-section delay-300">
            <div class="relative max-w-4xl mx-auto rounded-2xl overflow-hidden shadow-2xl border border-gray-100 transform md:rotate-1 animate-float">
              <img 
                src="https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=AI%20video%20editing%20interface%20with%20chat%20interface%20and%20video%20preview&sign=9c1e812c0490578486ce1c585885db0c" 
                alt="即剪AI界面展示" 
                class="w-full h-auto"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent flex items-end">
                <div class="p-6 text-white">
                  <p class="font-medium">{{ $t('hero.videoPrompt') }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      <div class="max-w-7xl mx-auto px-4 py-16">
        <!-- 用户痛点和解决方案 -->
        <div class="fade-in-section delay-100">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
            <div class="lg:col-span-2">
              <UserPainPoints />
            </div>
            <div class="lg:col-span-1">
              <VideoEditingFeature />
            </div>
          </div>
        </div>
        
        <!-- 解决方案展示 -->
        <div class="fade-in-section delay-200">
          <div class="mb-16">
            <h2 class="text-3xl font-bold text-center mb-12">{{ $t('solutions.title') }}</h2>
            <SolutionsShowcase />
          </div>
        </div>
        
        <!-- 项目展示区 -->
        <div class="fade-in-section delay-300">
          <div class="mb-16">
            <ProjectsSection />
          </div>
        </div>
        
        <!-- 功能介绍区 -->
        <div class="fade-in-section delay-400">
          <div>
            <FeaturesSection />
          </div>
        </div>
        
        <!-- CTA Section -->
        <div class="fade-in-section delay-500">
          <div class="mt-20 bg-gradient-primary rounded-2xl p-8 md:p-12 text-white text-center shadow-xl">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">{{ $t('cta.title') }}</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto text-white/90">
              {{ $t('cta.subtitle') }}
            </p>
            <el-button 
              size="large"
              class="px-8 py-4 bg-white text-jijian-primary rounded-lg text-lg font-medium shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1"
              @click="handleGetStarted"
            >
              {{ $t('cta.getStarted') }} 
              <el-icon class="ml-2"><Rocket /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </main>
    
    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-100 py-12 mt-16">
      <div class="max-w-7xl mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          <div>
            <div class="logo text-2xl font-bold mb-4">
              <span class="text-gradient">即剪AI</span>
            </div>
            <p class="text-jijian-text-light mb-4">用对话创造精彩视频，AI驱动的视频剪辑革命。</p>
            <div class="flex space-x-4">
              <a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">
                <el-icon><Platform /></el-icon>
              </a>
              <a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">
                <el-icon><ChatDotRound /></el-icon>
              </a>
              <a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">
                <el-icon><VideoPlay /></el-icon>
              </a>
              <a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">
                <el-icon><Link /></el-icon>
              </a>
            </div>
          </div>
          
          <div>
            <h3 class="font-bold text-lg mb-4">产品</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">功能</a></li>
              <li><a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">定价</a></li>
              <li><a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">教程</a></li>
              <li><a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">更新日志</a></li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-bold text-lg mb-4">公司</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">关于我们</a></li>
              <li><a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">联系方式</a></li>
              <li><a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">招贤纳士</a></li>
              <li><a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">媒体资源</a></li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-bold text-lg mb-4">法律</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">隐私政策</a></li>
              <li><a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">服务条款</a></li>
              <li><a href="#" class="text-jijian-text-light hover:text-jijian-primary transition-colors">版权声明</a></li>
            </ul>
          </div>
        </div>
        
        <div class="pt-8 border-t border-gray-100 flex flex-col md:flex-row justify-between items-center">
          <div class="text-sm text-jijian-text-light mb-4 md:mb-0">
            © 2023 即剪AI. 保留所有权利.
          </div>
          <div class="flex space-x-6">
            <a href="#" class="text-sm text-jijian-text-light hover:text-jijian-primary transition-colors">隐私政策</a>
            <a href="#" class="text-sm text-jijian-text-light hover:text-jijian-primary transition-colors">服务条款</a>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import { 
  ArrowRight, 
  VideoPlay, 
  Rocket, 
  Platform, 
  ChatDotRound, 
  Link 
} from '@element-plus/icons-vue'

// 导入组件
import JijianHeader from '@/components/jijian-ui/JijianHeader.vue'
import JijianLoginModal from '@/components/jijian-ui/JijianLoginModal.vue'

// 临时导入原有组件（后续需要重构）
import UserPainPoints from '@/components/UserPainPoints.vue'
import VideoEditingFeature from '@/components/VideoEditingFeature.vue'
import SolutionsShowcase from '@/components/SolutionsShowcase.vue'
import ProjectsSection from '@/components/ProjectsSection.vue'
import FeaturesSection from '@/components/FeaturesSection.vue'

// 组合式API
const router = useRouter()
const { t } = useI18n()
const userStore = useUserStore()

// 响应式数据
const showLoginModal = ref(false)
const showRegisterModal = ref(false)

// 方法
const handleTryFree = () => {
  if (userStore.isLoggedIn) {
    router.push('/upload')
  } else {
    showRegisterModal.value = true
  }
}

const handleWatchDemo = () => {
  // 滚动到演示区域或播放演示视频
  const demoSection = document.querySelector('#demo')
  if (demoSection) {
    demoSection.scrollIntoView({ behavior: 'smooth' })
  }
}

const handleGetStarted = () => {
  if (userStore.isLoggedIn) {
    router.push('/upload')
  } else {
    showRegisterModal.value = true
  }
}

const goToUpload = () => {
  router.push('/upload')
}

const handleLoginSuccess = (data: any) => {
  userStore.setUserInfo(data.user)
  userStore.setToken(data.token)
  userStore.setLoginStatus(true)
  ElMessage.success('登录成功！')
}

const handleRegisterSuccess = (data: any) => {
  userStore.setUserInfo(data.user)
  userStore.setLoginStatus(true)
  ElMessage.success('注册成功！')
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // 跳转到个人资料页面
      break
    case 'settings':
      // 跳转到设置页面
      break
    case 'logout':
      userStore.logout()
      ElMessage.success('已退出登录')
      break
  }
}

// 生命周期
onMounted(() => {
  // 添加滚动动画观察器
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '-100px 0px'
  }

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-fade-in')
      }
    })
  }, observerOptions)

  // 观察所有需要动画的元素
  document.querySelectorAll('.fade-in-section').forEach(el => {
    observer.observe(el)
  })
})
</script>

<style scoped>
.fade-in-section {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease-out;
}

.fade-in-section.animate-fade-in {
  opacity: 1;
  transform: translateY(0);
}

.delay-100 { transition-delay: 0.1s; }
.delay-200 { transition-delay: 0.2s; }
.delay-300 { transition-delay: 0.3s; }
.delay-400 { transition-delay: 0.4s; }
.delay-500 { transition-delay: 0.5s; }

.text-gradient {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}
</style>
