<template>
  <!-- 完全复制源项目的Home页面结构 -->
  <div class="min-h-screen bg-[var(--bg-light)]">
    <!-- Header组件 -->
    <JijianHeader
      :is-authenticated="userStore.isLoggedIn"
      @login-click="showLoginModal = true"
    />

    <!-- 登录弹窗 -->
    <JijianLoginModal
      v-model="showLoginModal"
      @login-success="handleLoginSuccess"
    />

    <main class="main-content pt-24">
      <!-- Hero Section - 完全复制源项目 -->
      <section class="relative overflow-hidden bg-gradient-to-b from-white to-[var(--bg-light)] py-16 md:py-24">
        <div class="absolute top-0 right-0 w-1/2 h-full bg-[var(--primary)]/5 rounded-l-full -z-10"></div>

        <!-- 浮动装饰图标 -->
        <div class="absolute top-20 left-10 w-16 h-16 bg-[var(--primary)]/10 rounded-full flex items-center justify-center animate-float" style="animation-delay: 0s;">
          <i class="fa-solid fa-video text-[var(--primary)] text-2xl"></i>
        </div>
        <div class="absolute top-40 right-20 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center animate-float" style="animation-delay: 1s;">
          <i class="fa-solid fa-magic-wand-sparkles text-blue-500 text-lg"></i>
        </div>
        <div class="absolute bottom-40 left-20 w-14 h-14 bg-green-100 rounded-full flex items-center justify-center animate-float" style="animation-delay: 2s;">
          <i class="fa-solid fa-scissors text-green-500 text-xl"></i>
        </div>
        <div class="absolute top-60 right-40 w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center animate-float" style="animation-delay: 3s;">
          <i class="fa-solid fa-sparkles text-yellow-500"></i>
        </div>
        <div class="absolute bottom-60 right-10 w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center animate-float" style="animation-delay: 4s;">
          <i class="fa-solid fa-wand-magic text-pink-500 text-lg"></i>
        </div>
        <div class="max-w-7xl mx-auto px-4 relative z-10">
          <FadeInSection :delay="0.1">
            <div class="text-center max-w-4xl mx-auto mb-16">
              <h1 class="font-bold mb-8 leading-tight text-[var(--text-dark)]" style="font-size: 3.75rem;">
                用<span class="text-gradient">对话</span>创造精彩视频
              </h1>
              <p class="text-[var(--text-light)] mb-12" style="font-size: 1.25rem; line-height: 1.75rem;">
                即剪AI让视频创作变得前所未有的简单。只需输入文字描述，AI就能帮你剪辑出专业级视频。
              </p>
              <div class="flex flex-col sm:flex-row justify-center gap-4">
                <button
                  class="px-8 py-3 bg-gradient-primary text-white rounded-lg text-lg font-medium shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 hover:scale-105"
                  @click="handleTryFree"
                >
                  免费试用 <i class="fa-solid fa-arrow-right ml-2"></i>
                </button>
                <button
                  class="px-8 py-3 bg-white text-[var(--text-dark)] rounded-lg text-lg font-medium shadow-lg hover:shadow-xl transition-all flex items-center justify-center hover:scale-105 border border-gray-200 hover:border-[var(--primary)]"
                  @click="handleWatchDemo"
                >
                  <i class="fa-solid fa-play-circle mr-2 text-[var(--primary)]"></i> 观看演示
                </button>
              </div>
            </div>
          </FadeInSection>

          <FadeInSection :delay="0.3">
            <div class="relative max-w-5xl mx-auto rounded-2xl overflow-hidden shadow-2xl border border-gray-100 transform md:rotate-1 animate-float">
              <img
                src="https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2339&q=80"
                alt="即剪AI界面展示"
                class="w-full h-auto"
                loading="lazy"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent flex items-end">
                <div class="p-8 text-white">
                  <div class="bg-white/20 backdrop-blur-sm rounded-lg p-4 inline-block">
                    <div class="flex items-center space-x-3">
                      <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                      <p class="font-semibold text-lg">只需输入文字，AI自动生成专业视频</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      <!-- 特色功能展示区 -->
      <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4">
          <FadeInSection :delay="0.1">
            <div class="text-center mb-16">
              <h2 class="font-bold text-[var(--text-dark)] mb-6" style="font-size: 3rem;">
                为什么选择<span class="text-gradient">即剪AI</span>？
              </h2>
              <p class="text-[var(--text-light)] max-w-3xl mx-auto" style="font-size: 1.25rem; line-height: 1.75rem;">
                革命性的AI技术，让视频创作变得简单、高效、专业
              </p>
            </div>
          </FadeInSection>

          <!-- 特色功能卡片 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <FadeInSection :delay="0.2">
              <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 text-center hover:transform hover:-translate-y-2 transition-all duration-300 shadow-lg hover:shadow-xl">
                <div class="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                  <i class="fa-solid fa-robot text-white text-3xl"></i>
                </div>
                <h3 class="text-2xl font-bold text-[var(--text-dark)] mb-4">智能AI剪辑</h3>
                <p class="text-[var(--text-light)] text-lg">先进的AI算法自动识别精彩片段，智能剪辑生成专业视频</p>
              </div>
            </FadeInSection>

            <FadeInSection :delay="0.3">
              <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-8 text-center hover:transform hover:-translate-y-2 transition-all duration-300 shadow-lg hover:shadow-xl">
                <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                  <i class="fa-solid fa-comments text-white text-3xl"></i>
                </div>
                <h3 class="text-2xl font-bold text-[var(--text-dark)] mb-4">自然语言交互</h3>
                <p class="text-[var(--text-light)] text-lg">用简单的对话描述需求，AI理解并执行复杂的视频编辑任务</p>
              </div>
            </FadeInSection>

            <FadeInSection :delay="0.4">
              <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-8 text-center hover:transform hover:-translate-y-2 transition-all duration-300 shadow-lg hover:shadow-xl">
                <div class="w-20 h-20 bg-[var(--primary)] rounded-full flex items-center justify-center mx-auto mb-6">
                  <i class="fa-solid fa-bolt text-white text-3xl"></i>
                </div>
                <h3 class="text-2xl font-bold text-[var(--text-dark)] mb-4">极速生成</h3>
                <p class="text-[var(--text-light)] text-lg">几分钟内完成传统需要数小时的视频剪辑工作</p>
              </div>
            </FadeInSection>
          </div>

          <!-- 数据统计展示 -->
          <FadeInSection :delay="0.5">
            <div class="bg-gradient-to-r from-[var(--primary)]/5 to-blue-500/5 rounded-2xl p-12">
              <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div>
                  <div class="text-4xl font-bold text-[var(--primary)] mb-2">10万+</div>
                  <div class="text-[var(--text-light)]">用户信赖</div>
                </div>
                <div>
                  <div class="text-4xl font-bold text-[var(--primary)] mb-2">500万+</div>
                  <div class="text-[var(--text-light)]">视频生成</div>
                </div>
                <div>
                  <div class="text-4xl font-bold text-[var(--primary)] mb-2">95%</div>
                  <div class="text-[var(--text-light)]">时间节省</div>
                </div>
                <div>
                  <div class="text-4xl font-bold text-[var(--primary)] mb-2">24/7</div>
                  <div class="text-[var(--text-light)]">在线服务</div>
                </div>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      <div class="max-w-7xl mx-auto px-4 py-16">
        <!-- 用户痛点和视频编辑功能 - 完全复制源项目布局 -->
        <FadeInSection :delay="0.1">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
            <div class="lg:col-span-2">
              <UserPainPoints />
            </div>
            <div class="lg:col-span-1">
              <VideoEditingFeature />
            </div>
          </div>
        </FadeInSection>

        <!-- 解决方案展示 - 完全复制源项目 -->
        <FadeInSection :delay="0.2">
          <div class="mb-16">
            <h2 class="text-3xl font-bold text-center mb-12">AI视频创作新体验</h2>
            <SolutionsShowcase />
          </div>
        </FadeInSection>

        <!-- 项目展示区 - 完全复制源项目 -->
        <FadeInSection :delay="0.3">
          <div class="mb-16">
            <ProjectsSection />
          </div>
        </FadeInSection>

        <!-- 功能介绍区 - 完全复制源项目 -->
        <FadeInSection :delay="0.4">
          <div>
            <FeaturesSection />
          </div>
        </FadeInSection>

        <!-- CTA Section - 增强版 -->
        <FadeInSection :delay="0.5">
          <div class="mt-20 bg-gradient-primary rounded-3xl p-12 md:p-16 text-white text-center shadow-2xl relative overflow-hidden">
            <!-- 装饰性背景元素 -->
            <div class="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
            <div class="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>

            <div class="relative z-10">
              <div class="inline-flex items-center bg-white/20 rounded-full px-6 py-2 mb-6">
                <i class="fa-solid fa-star text-yellow-300 mr-2"></i>
                <span class="text-sm font-medium">限时免费体验</span>
              </div>

              <h2 class="font-bold mb-6" style="font-size: 3rem;">
                开始你的AI视频创作之旅
              </h2>
              <p class="mb-10 max-w-3xl mx-auto text-white/90" style="font-size: 1.25rem; line-height: 1.75rem;">
                无需复杂操作，用文字释放你的创意潜能。立即体验AI驱动的视频剪辑革命。
              </p>

              <div class="flex flex-col sm:flex-row justify-center gap-4">
                <button
                  class="px-8 py-4 bg-white text-[var(--primary)] rounded-lg text-lg font-semibold shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 hover:scale-105"
                  @click="handleGetStarted"
                >
                  <i class="fa-solid fa-rocket mr-2"></i>
                  免费开始使用
                </button>
                <button
                  class="px-8 py-4 bg-transparent border-2 border-white text-white rounded-lg text-lg font-semibold hover:bg-white hover:text-[var(--primary)] transition-all transform hover:-translate-y-1 hover:scale-105"
                  @click="handleWatchDemo"
                >
                  <i class="fa-solid fa-play mr-2"></i>
                  观看演示
                </button>
              </div>

              <div class="mt-8 text-white/70">
                <p class="text-sm">✨ 无需信用卡 • 🚀 即刻开始 • 💎 专业品质</p>
              </div>
            </div>
          </div>
        </FadeInSection>
      </div>
    </main>
    
    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-100 py-12 mt-16">
      <div class="max-w-7xl mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          <div>
            <div class="logo text-2xl font-bold mb-4">
              <span class="text-gradient">即剪AI</span>
            </div>
            <p class="text-[var(--text-light)] mb-4">用对话创造精彩视频，AI驱动的视频剪辑革命。</p>
            <div class="flex space-x-4">
              <a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"><i class="fa-brands fa-twitter"></i></a>
              <a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"><i class="fa-brands fa-instagram"></i></a>
              <a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"><i class="fa-brands fa-youtube"></i></a>
              <a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"><i class="fa-brands fa-github"></i></a>
            </div>
          </div>
          
          <div>
            <h3 class="font-bold text-lg mb-4">产品</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">功能</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">定价</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">教程</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">更新日志</a></li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-bold text-lg mb-4">公司</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">关于我们</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">联系方式</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">招贤纳士</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">媒体资源</a></li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-bold text-lg mb-4">法律</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">隐私政策</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">服务条款</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">版权声明</a></li>
            </ul>
          </div>
        </div>
        
        <div class="pt-8 border-t border-gray-100 flex flex-col md:flex-row justify-between items-center">
          <div class="text-sm text-[var(--text-light)] mb-4 md:mb-0">
            © 2023 即剪AI. 保留所有权利.
          </div>
          <div class="flex space-x-6">
            <a href="#" class="text-sm text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">隐私政策</a>
            <a href="#" class="text-sm text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">服务条款</a>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, defineComponent, h } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'

// 导入组件 - 完全复制源项目结构
import JijianHeader from '@/components/jijian-ui/JijianHeader.vue'
import InputSection from '@/components/jijian-ui/InputSection.vue'
import UserPainPoints from '@/components/UserPainPoints.vue'
import SolutionsShowcase from '@/components/SolutionsShowcase.vue'
import VideoEditingFeature from '@/components/VideoEditingFeature.vue'
import ProjectsSection from '@/components/ProjectsSection.vue'
import FeaturesSection from '@/components/FeaturesSection.vue'
import JijianLoginModal from '@/components/jijian-ui/JijianLoginModal.vue'

// 简化的渐入动画组件
const FadeInSection = defineComponent({
  props: {
    delay: {
      type: Number,
      default: 0
    }
  },
  setup(props, { slots }) {
    return () => h('div', {
      class: 'fade-in-section',
      style: {
        '--delay': `${props.delay}s`,
        opacity: 0,
        transform: 'translateY(20px)',
        transition: `all 0.6s ease ${props.delay}s`
      }
    }, slots.default?.())
  }
})

// 组合式API
const router = useRouter()
const { t } = useI18n()
const userStore = useUserStore()

// 响应式数据
const showLoginModal = ref(false)

// 方法 - 完全复制源项目逻辑
const handleTryFree = () => {
  if (userStore.isLoggedIn) {
    router.push('/upload')
  } else {
    showLoginModal.value = true
  }
}

const handleWatchDemo = () => {
  // 滚动到演示区域
  const demoSection = document.querySelector('.solutions-showcase')
  if (demoSection) {
    demoSection.scrollIntoView({ behavior: 'smooth' })
  }
}

const handleGetStarted = () => {
  if (userStore.isLoggedIn) {
    router.push('/upload')
  } else {
    showLoginModal.value = true
  }
}

const handleLoginSuccess = () => {
  userStore.setLoginStatus(true)
  showLoginModal.value = false
  ElMessage.success('登录成功！')
}
</script>

<style scoped>
.fade-in-section {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease-out;
}

.fade-in-section.animate-fade-in {
  opacity: 1;
  transform: translateY(0);
}

/* 自动触发动画 */
.fade-in-section {
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}
</style>
