<!--
  * 视频剪辑页面主组件
  * 包含左侧菜单、中间播放器和属性面板、底部轨道编辑区域
  * 实现了视频剪辑的核心功能
-->
<template>
  <!-- 主容器：视频编辑器布局 -->
  <div
    class="w-full h-full flex flex-row overflow-hidden editor-main-container"
    @click="closeContextMenu"
  >
    <!-- 左侧菜单区域 -->
    <div
      id="left"
      class="h-screen left-panel-container editor-panel"
      v-show="leftMenuVisible"
      :style="{ width: leftPanelWidth + 'px' }"
    >
      <ClipMenu @toggle-visibility="handleLeftMenuToggle" />

      <!-- 调试信息 -->
      <div
        v-if="false"
        class="fixed top-4 left-4 bg-black bg-opacity-75 text-white p-2 rounded text-xs z-50"
      >
        左侧面板: {{ leftPanelWidth }}px
      </div>
    </div>

    <!-- 中间主要内容区域 -->
    <div id="right" class="h-screen overflow-hidden flex-1">
      <!-- 顶部区域：包含播放器和属性面板 -->
      <div id="top" class="flex">
        <!-- 播放器区域 -->
        <div class="flex-1">
          <Player
            ref="playerRef"
            :tracks="tracks"
            :currentTime="currentTime"
            :playerDuration="playerDuration"
            @prevFrame="prevFrame"
            @nextFrame="nextFrame"
            @timeUpdate="timeUpdate"
            @captureImage="captureImage"
            @updateClipProps="updateClipProps"
          />
        </div>
        <!-- 右侧可拉伸面板 -->
        <div
          v-show="rightPanelVisible"
          class="relative editor-panel editor-panel-right"
          :style="{ width: rightPanelWidth + 'px' }"
        >
          <!-- 拖拽调整手柄 -->
          <div
            class="right-panel-resize-handle"
            @mousedown="startRightPanelResize"
            title="拖拽调整面板宽度"
          ></div>

          <!-- 面板内容 -->
          <div class="h-full flex flex-col">
            <!-- 面板头部 -->
            <div class="editor-panel-header">
              <div class="flex items-center space-x-2">
                <span class="editor-panel-title">属性面板</span>
                <div class="editor-panel-badge">
                  {{ rightPanelWidth }}px
                </div>
              </div>
              <button
                @click="handleRightPanelToggle"
                class="editor-close-btn"
                title="关闭面板"
              >
                ×
              </button>
            </div>

            <!-- 面板主体内容 -->
            <div class="flex-1 overflow-hidden">
              <ClipProperties
                :clip="selectedClip"
                :min-clip-duration="minClipDuration"
                @update="updateClipProps"
                @toggle-visibility="handleRightPanelToggle"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 底部轨道编辑区域 -->
      <div id="bottom">
        <!-- 视图切换控制栏 -->
        <div class="editor-toolbar">
          <div class="editor-view-switcher">
            <button
              :class="['editor-view-btn', { active: timelineViewMode === 'traditional' }]"
              @click="switchToTraditionalView"
              title="传统时间轴视图"
            >
              传统视图
            </button>
            <button
              :class="['editor-view-btn', { active: timelineViewMode === 'cards' }]"
              @click="switchToCardsView"
              title="片段卡片视图"
            >
              卡片视图
            </button>
          </div>

          <!-- 视图状态指示器 -->
          <div class="view-status">
            <span class="status-text">
              {{ timelineViewMode === 'traditional' ? '传统时间轴' : '片段卡片' }}
            </span>
            <span class="compatibility-badge">
              <span class="badge-icon">✅</span>
              <span class="badge-text">数据同步</span>
            </span>
          </div>
        </div>

        <!-- 动态视图内容 -->
        <div class="timeline-view-container">
          <!-- 传统时间轴视图 -->
          <ClipTrack
            v-show="timelineViewMode === 'traditional'"
            ref="clipTrackRef"
            :tracks="tracks"
            :trackZoom="trackZoom"
            :maxTracksNum="MAX_TRACKS_NUM"
            :currentTime="currentTime"
            :timelineDuration="timelineDuration"
            :BASE_TICK_SPACING="BASE_TICK_SPACING"
            :MIN_CLIP_WIDTH="MIN_CLIP_WIDTH"
            :frame-length="frameLength"
            :min-clip-duration="minClipDuration"
            @stopPlay="handleStopPlay"
            @activeClip="handleActiveClip"
            @updateTrackZoom="updateTrackZoom"
            @timeUpdate="timeUpdate"
            @add-clip="addPlayerClip"
            @refreshPlayer="refreshPlayer"
            @delete-empty-track="deleteEmptyTrack"
            @handle-export="handleExport"
          />

          <!-- 片段卡片视图 -->
          <ClipCardsView
            v-show="timelineViewMode !== 'traditional'"
            :tracks="tracks"
            :currentTime="currentTime"
            :selectedClips="selectedClips"
            :timelineDuration="timelineDuration"
            @activeClip="handleActiveClip"
            @updateTracks="updateTracks"
            @timeUpdate="timeUpdate"
            @refreshPlayer="refreshPlayer"
            @addClip="handleAddClip"
          />
        </div>
      </div>
    </div>

    <!-- 右侧面板：AI助手 -->
    <div
      id="rightPanel"
      class="h-screen border-l border-[#4b5563] flex flex-col relative"
      :style="{ width: aiAssistantWidth + 'px' }"
      v-show="aiAssistantVisible"
    >
      <!-- AI助手面板拖拽分隔条 -->
      <div
        class="ai-resize-handle"
        @mousedown="startAiPanelResize"
        title="拖拽调整AI助手面板宽度"
      >
        <div class="ai-resize-indicator"></div>
      </div>
      <!-- AI助手面板 -->
      <div class="flex-1 min-h-0">
        <AiChatPanel
          :tracks="tracks"
          :currentTime="currentTime"
          :selectedClips="selectedClips"
          :activeTrack="activeTrack"
          :playerRef="playerRef"
          :clipTrackRef="clipTrackRef"
          :projectId="projectId"
          :timelineDuration="timelineDuration"
          @commandExecuted="handleCommandExecuted"
          @tracksUpdated="handleTracksUpdated"
          @switchToTraditionalView="switchToTraditionalView"
          @close="handleCloseAIAssistant"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 导入所需的组件和工具
 */
// Vue APIs are auto-imported by unplugin-auto-import
import { nextTick } from 'vue';
import Player from './clipPlayer/Player.vue';
import { useTrackStore } from '@/store/modules/track';
import Split from 'split.js';
import ClipTrack from './clipTrack/clipTrack.vue';
import ClipProperties from './ClipProperties/ClipProperties.vue';
import ClipMenu from './clipMenu/clipMenu.vue';
import AiChatPanel from '@/components/ai-assistant/AiChatPanel.vue';
import ClipCardsView from './components/ClipCardsView.vue';
import { useRoute } from 'vue-router';
import { db, Media } from '@/db/db';
import { Track, TrackClip } from '@/types/track';
import { getKeyframes, getVolume } from '@/components/js/webcodecs';
import { cloneDeep } from 'lodash';
import { ElMessage } from 'element-plus';
import { dataURLToBuffer } from '@/utils/opfs-file';
import { write } from 'opfs-tools';
import { videoProcessingService } from '@/services/videoProcessingService';

/**
 * 禁用浏览器默认的缩放行为
 * 防止Ctrl+滚轮触发浏览器缩放，影响编辑体验
 */
const disableZoom = (e: WheelEvent) => {
  if (e.ctrlKey) {
    e.preventDefault();
  }
};

onMounted(() => {
  window.addEventListener('wheel', disableZoom, { passive: false });
});

onUnmounted(() => {
  window.removeEventListener('wheel', disableZoom);
});

const route = useRoute();
const trackStore = useTrackStore();
// 🎯 项目ID可能是数字或字符串（如 task_xxx）
const projectId = route.params.id as string;

// 🎯 辅助函数：将字符串项目ID转换为数字（用于历史记录）
const getNumericProjectId = (id: string): number => {
  if (id.startsWith('task_')) {
    return parseInt(id.replace('task_', ''));
  }
  return parseInt(id) || 0;
};
const MAX_TRACKS_NUM = 10; // 最大轨道数量
const MIN_DURATION = 300; // 最小时长5分钟（秒）
const BUFFER_DURATION = 5; // 缓冲时长（秒）

// 布局系统重构 - 精确的响应式布局管理
const LAYOUT_STORAGE_KEY = 'layout-panel-states';
const SPLIT_STORAGE_KEY = 'split-sizes';

// 视口尺寸管理
const viewportWidth = ref(window.innerWidth);
const viewportHeight = ref(window.innerHeight);

// AI助手状态管理
const aiAssistantVisible = ref(true);
const aiAssistantWidth = ref(320); // AI助手面板宽度
const minAiAssistantWidth = 280; // 最小宽度
const maxAiAssistantWidth = 600; // 最大宽度
let isResizingAiPanel = false; // 拖拽状态

// 面板状态管理
const leftMenuVisible = ref(true);   // 左侧菜单显示状态（始终为true，菜单栏始终可见）
const rightPanelVisible = ref(true); // 右侧属性面板显示状态

// 右侧面板宽度管理
const rightPanelWidth = ref(320); // 默认宽度320px
const minRightPanelWidth = 280;   // 最小宽度
const maxRightPanelWidth = 600;   // 最大宽度
let isResizingRightPanel = false; // 是否正在调整右侧面板大小

// 左侧面板宽度管理
const leftPanelWidth = ref(280);  // 左侧面板总宽度（菜单栏 + 内容区域）
const leftMenuSidebarWidth = 80; // 菜单栏固定宽度

// 左侧菜单面板状态管理
const leftPanelStates = ref({
  media: false,
  audio: false,
  audioEffects: false,
  text: false,
  filter: false
});

// 当前显示的左侧面板类型
const currentLeftPanel = ref<string | null>(null);

// 精确的布局计算系统
const layoutConfig = computed(() => {
  // 计算左侧面板的实际宽度
  const hasActiveLeftPanel = Object.values(leftPanelStates.value).some(state => state);
  const leftActualWidth = hasActiveLeftPanel ? leftPanelWidth.value : leftMenuSidebarWidth; // 动态宽度

  // 计算可用的视口宽度
  const availableWidth = viewportWidth.value;
  const rightAIWidth = aiAssistantVisible.value ? 320 : 0;
  const minCenterWidth = 600; // 确保中间内容区域至少600px

  // 计算百分比
  const leftPercent = (leftActualWidth / availableWidth) * 100;
  const rightPercent = (rightAIWidth / availableWidth) * 100;
  const centerPercent = 100 - leftPercent - rightPercent;

  // 验证布局是否有效
  const actualCenterWidth = (centerPercent / 100) * availableWidth;
  const isLayoutValid = actualCenterWidth >= minCenterWidth;

  console.log('📐 布局计算:', {
    viewportWidth: availableWidth,
    leftWidth: leftActualWidth,
    centerWidth: actualCenterWidth,
    rightWidth: rightAIWidth,
    percentages: { left: leftPercent, center: centerPercent, right: rightPercent },
    isValid: isLayoutValid
  });

  const config = {
    // 基础配置
    leftWidth: leftActualWidth,
    rightWidth: rightPanelVisible.value ? rightPanelWidth.value : 0,
    aiWidth: rightAIWidth,
    centerWidth: actualCenterWidth,
    isValid: isLayoutValid,

    // 计算中间内容区域宽度百分比
    getTotalFixedWidth() {
      return this.leftWidth + this.aiWidth;
    },

    // 获取水平分割配置
    getHorizontalSizes() {
      if (!isLayoutValid) {
        // 如果布局无效，优先保证中间内容区域
        return hasActiveLeftPanel ? [15, 85, 0] : [6, 94, 0];
      }

      // 正常布局
      const sizes = [];
      if (leftPercent > 0) sizes.push(Math.max(leftPercent, 6));
      if (centerPercent > 0) sizes.push(Math.max(centerPercent, 40));
      if (rightPercent > 0) sizes.push(Math.max(rightPercent, 15));

      return sizes;
    },

    // 获取最小尺寸配置
    getMinSizes() {
      const sizes = [];
      if (leftActualWidth > 0) sizes.push(hasActiveLeftPanel ? 280 : 80);
      sizes.push(minCenterWidth); // 中间内容最小宽度
      if (rightAIWidth > 0) sizes.push(300);

      return sizes;
    }
  };

  return config;
});

// 轨道相关常量
const BASE_TICK_SPACING = 3; // 基础刻度间距(px)
const MIN_CLIP_WIDTH = 30; // 最小片段宽度(px)
const ZOOM_STORAGE_KEY = 'track-zoom-value';

// 从 localStorage 获取缩放值
const trackZoom = ref(
  parseFloat(localStorage.getItem(ZOOM_STORAGE_KEY) || '1')
);

const updateTrackZoom = (zoom: number) => {
  trackZoom.value = zoom;
  localStorage.setItem(ZOOM_STORAGE_KEY, zoom.toString());
};
// 计算属性
const frameLength = computed(() => BASE_TICK_SPACING * trackZoom.value * 10); // 每秒占据的像素
const minClipDuration = computed(() => MIN_CLIP_WIDTH / frameLength.value); // 根据缩放计算最小持续时间

// 轨道数据
const tracks = ref<Track[]>([]);
const currentTime = ref(0);
const selectedClips = ref<string[]>([]);
const activeTrack = ref<string | null>(null);
const playerRef = ref();
const clipTrackRef = ref();
const selectedClip = ref(null);

// 视图模式状态 - 新增功能
const timelineViewMode = ref<'traditional' | 'cards'>('traditional'); // 默认传统视图

// AI助手状态管理 - 使用新的布局系统


// 计算时间轴的总时长
const timelineDuration = computed(() => {
  // 最大endTime加缓冲时长，且不小于最小时长
  return Math.max(playerDuration.value + BUFFER_DURATION, MIN_DURATION);
});
// 计算播放器总时长
const playerDuration = computed(() => {
  // 找出所有轨道中最大的endTime
  let maxEndTime = 0;
  tracks.value.forEach((track) => {
    track.clips.forEach((clip) => {
      const endTime = Number(clip.endTime);
      if (endTime > maxEndTime) {
        maxEndTime = endTime;
      }
    });
  });
  return maxEndTime;
});

// playerRef 和 clipTrackRef 已在上面声明

const timeUpdate = (time: number) => {
  currentTime.value = time;
};

const captureImage = async (dataUrl: string) => {
  const buffer = dataURLToBuffer(dataUrl);
  await write('/capture/' + projectId + '.png', buffer, {
    overwrite: true,
  });
  const project = await db.projects.where({ id: projectId }).first();
  if (project) {
    db.projects.update(project, {
      thumbnail: '/capture/' + projectId + '.png',
    });
  }
};

const prevFrame = () => {
  currentTime.value -= 30 / 1000 / 1000;
};

const nextFrame = () => {
  currentTime.value += 30 / 1000 / 1000;
};

// 提供 addClip 方法给所有子组件使用
provide('addClip', async (clip: TrackClip, createNewTrack?: boolean) => {
  // 设置 clip 的开始时间为当前时间
  clip.startTime = currentTime.value;
  clip.endTime = currentTime.value + clip.duration;

  // 对于视频类型，设置源时间相关属性
  if (clip.type === 'video') {
    clip.sourceStartTime = 0;
    clip.sourceEndTime = 0;
    clip.originalDuration = clip.duration;
  }

  // 获取视频关键帧或图片缩略图
  if (clip.type === 'video' || clip.type === 'image') {
    const res = await getKeyframes(clip as Media);
    clip.thumbnail = res.data as { url: string; timestamp: number }[];
  }

  // 获取音频波形数据
  if (clip.type === 'audio') {
    const res = await getVolume(clip as Media);
    if (res.type === 'audio') {
      clip.volumeData = res.data;
    }
  }

  // 如果需要创建新轨道
  if (createNewTrack) {
    if (tracks.value.length >= MAX_TRACKS_NUM) {
      ElMessage.warning(`轨道数量已达到最大限制(${MAX_TRACKS_NUM})`);
      return;
    }
    const newTrack: Track = {
      id: String(tracks.value.length),
      clips: [clip],
    };
    tracks.value.push(newTrack);
  } else {
    // 否则添加到最后一个轨道
    if (tracks.value.length === 0) {
      tracks.value.push({
        id: '0',
        clips: [],
      });
    }
    tracks.value[tracks.value.length - 1].clips.push(clip);
  }
  // 添加片段到播放器（不需要重新创建整个Canvas）
  nextTick(() => {
    playerRef.value?.addClip(clip);
  });
});

// 提供 activeClip 方法，在需要时激活轨道中的clip
provide('activeClip', (id: string | null) => {
  if (id) {
    const clip = tracks.value
      .find((track) => track.clips.some((clip) => clip.id === id))
      ?.clips.find((clip) => clip.id === id);
    if (clip) {
      clipTrackRef.value?.activeClip(clip);
      selectedClip.value = clip;
    }
  } else {
    clipTrackRef.value?.activeClip(null);
    selectedClip.value = null;
  }
});

const playerWidth = ref(0);
const playerHeight = ref(0);
// 🎯 立即检查 sessionStorage（在页面加载时）
console.log('🚀 剪辑页面脚本开始执行...');
const immediateSessionCheck = sessionStorage.getItem('videoEditorData');
console.log('🔍 立即检查 sessionStorage:', {
  hasData: !!immediateSessionCheck,
  dataLength: immediateSessionCheck?.length || 0
});

onMounted(async () => {
  console.log('🚀 页面初始化开始');

  // 恢复所有面板状态
  restorePanelStates();

  // 恢复右侧面板宽度
  restoreRightPanelWidth();

  // 恢复AI助手面板宽度
  restoreAiAssistantWidth();

  // 监听来自菜单的AI助手切换事件
  window.addEventListener('toggleAIAssistant', handleToggleAIAssistantEvent);

  // 监听面板状态变化事件
  window.addEventListener('panelStateChange', handlePanelStateChange);

  // 监听左侧面板宽度变化事件
  window.addEventListener('leftPanelWidthChange', handleLeftPanelWidthChange);

  // 🔧 监听实时宽度变化事件（拖拽过程中）
  window.addEventListener('leftPanelWidthChangeImmediate', handleLeftPanelWidthChangeImmediate);

  // 监听窗口大小变化，动态调整布局
  window.addEventListener('resize', handleWindowResize);

  // 清除旧的分割尺寸缓存，确保新的高度配置生效
  localStorage.removeItem(SPLIT_STORAGE_KEY);

  // 🎯 优先检查是否来自视频处理流程（sessionStorage 优先级更高）
  console.log('🔍 ===== 开始检查视频处理结果 =====');
  console.log('🔍 项目ID:', projectId);
  console.log('🔍 当前轨道数量（检查前）:', tracks.value.length);

  await checkAndLoadProcessingResult(projectId);

  console.log('✅ ===== 视频处理结果检查完成 =====');
  console.log('✅ 当前轨道数量（检查后）:', tracks.value.length);

  // 🔄 如果没有从 sessionStorage 加载到数据，再从数据库加载项目数据
  if (tracks.value.length === 0) {
    console.log('🔍 从数据库加载项目数据...');
    const project = await db.projects.where({ id: projectId }).first();
    if (project) {
      tracks.value = project.tracks;
      deleteEmptyTrack();
      // 🎯 历史记录需要数字ID
      const numericProjectId = getNumericProjectId(projectId);
      trackStore.clearHistory(numericProjectId);
      trackStore.saveHistoryState(numericProjectId, tracks.value);
      refreshPlayer();
      console.log('✅ 从数据库加载了项目数据，轨道数量:', tracks.value.length);
    }
  }

  // 🎯 上传完视频后一定有轨道数据，不需要创建默认轨道
  if (tracks.value.length === 0) {
    console.error('❌ 没有轨道数据，这不应该发生！');
    throw new Error('视频数据加载失败，没有找到轨道数据');
  }

  // 初始化布局系统（在数据加载后）
  await nextTick(); // 确保DOM渲染完成
  setTimeout(() => {
    initializeHorizontalSplit();
    initializeVerticalSplit();
  }, 300); // 延迟初始化确保所有组件都已挂载

  console.log('✅ 页面初始化完成');

  // 🎯 调试：检查 sessionStorage 中的数据
  const debugSessionData = sessionStorage.getItem('videoEditorData');
  console.log('🔍 调试 sessionStorage 数据:', {
    hasData: !!debugSessionData,
    dataLength: debugSessionData?.length || 0,
    dataPreview: debugSessionData ? debugSessionData.substring(0, 200) + '...' : null
  });
});

// 监听轨道数量变化，智能调整布局
watch(() => tracks.value.length, (newCount, oldCount) => {
  if (oldCount !== undefined && newCount !== oldCount) {
    console.log('🎬 轨道数量变化，智能调整布局:', {
      from: oldCount,
      to: newCount,
      config: getVerticalSplitConfig()
    });

    // 延迟重新初始化垂直分割，确保DOM更新完成
    nextTick(() => {
      setTimeout(() => {
        if (verticalSplitInstance) {
          verticalSplitInstance.destroy();
          verticalSplitInstance = null;
        }
        initializeVerticalSplit();
      }, 150);
    });
  }
}, { immediate: false });
onUnmounted(() => {
  console.log('🧹 页面卸载清理');

  // 清理事件监听器
  window.removeEventListener('toggleAIAssistant', handleToggleAIAssistantEvent);
  window.removeEventListener('panelStateChange', handlePanelStateChange);
  window.removeEventListener('resize', handleWindowResize);

  // 清理防抖定时器
  if (resizeTimeout.value) {
    clearTimeout(resizeTimeout.value);
    resizeTimeout.value = null;
  }

  // 销毁Split实例
  destroyExistingSplits();

  // 清理缩略图资源
  releaseThumbnail();

  console.log('✅ 页面清理完成');
});

const getThumbnail = async () => {
  tracks.value.forEach((track) => {
    track.clips.forEach((clip) => {
      if (clip.type === 'audio' && !clip.volumeData) {
        getVolume(clip as Media).then((res) => {
          if (res.type === 'audio') {
            clip.volumeData = res.data;
          }
        });
      } else if (clip.type === 'video' || clip.type === 'image') {
        if (clip.thumbnail && clip.thumbnail.length > 0) {
          const img = new Image();
          img.src = clip.thumbnail[0].url;
          img.onerror = () => {
            getKeyframes(clip as Media).then((res) => {
              clip.thumbnail = res.data as { url: string; timestamp: number }[];
            });
          };
        } else {
          getKeyframes(clip as Media).then((res) => {
            clip.thumbnail = res.data as { url: string; timestamp: number }[];
          });
        }
      }
    });
  });
};

const releaseThumbnail = () => {
  tracks.value.forEach((track) => {
    track.clips.forEach((clip) => {
      if (clip.thumbnail && clip.thumbnail.length > 0) {
        clip.thumbnail.forEach((item) => {
          URL.revokeObjectURL(item.url);
        });
      }
      clip.thumbnail = null;
    });
  });
};

const closeContextMenu = () => {
  trackStore.setShowContextMenu(false);
};

const updateClipProps = (updateClip: TrackClip) => {
  for (const track of tracks.value) {
    for (const clip of track.clips) {
      if (clip.id === updateClip.id) {
        Object.assign(clip, updateClip);
        break;
      }
    }
  }
};

watch(
  () => tracks.value,
  async (val) => {
    await saveProject();
  },
  {
    deep: true,
  }
);

// 在保存轨道数据时更新项目
const saveProject = async () => {
  const project = await db.projects.where({ id: projectId }).first();
  if (project) {
    // 需要深拷贝 tracks 数组,避免 IDB 克隆错误
    const rawTracks = cloneDeep(tracks.value);
    rawTracks.forEach((track) => {
      track.clips.forEach((clip) => {
        clip.thumbnail = null;
      });
    });
    await db.projects.update(project, { tracks: rawTracks });
  }
};

const handleActiveClip = (clip: TrackClip | null) => {
  selectedClip.value = clip;

  // 当选择片段时，自动显示属性面板
  if (clip) {
    rightPanelVisible.value = true;
    console.log('🎯 片段已选中，显示属性面板:', clip.name);
  }

  if (clip?.type !== 'audio') {
    playerRef.value?.activeClip(clip);
  }
};

const addPlayerClip = (clip: TrackClip) => {
  playerRef.value?.addClip(clip);
};

// AI 命令执行处理
const handleCommandExecuted = (data: { command: any; result: any }) => {
  console.log('AI 命令执行完成:', data);

  // 如果命令执行成功，可能需要刷新播放器
  if (data.result.success) {
    nextTick(() => {
      playerRef.value?.refreshPlayer();
    });
  }
};

// AI分析结果处理 - 更新tracks数据
const handleTracksUpdated = (updatedTracks: Track[]) => {
  console.log('🔄 AI分析更新tracks数据:', updatedTracks);
  console.log('🔍 更新前tracks.value:', tracks.value);
  console.log('🔍 更新后tracks数据详情:');
  updatedTracks.forEach((track, index) => {
    console.log(`  轨道 ${index}:`, track);
    if (track.clips) {
      track.clips.forEach((clip, clipIndex) => {
        console.log(`    片段 ${clipIndex}:`, {
          id: clip.id,
          name: clip.name,
          startTime: clip.startTime,
          endTime: clip.endTime,
          sourceStartTime: clip.sourceStartTime,
          sourceEndTime: clip.sourceEndTime,
          path: clip.path,
          originalPath: clip.originalPath
        });
      });
    }
  });

  tracks.value = updatedTracks;
  console.log('✅ tracks.value已更新，新长度:', tracks.value.length);

  // 保存到数据库
  nextTick(async () => {
    try {
      const project = await db.projects.where({ id: projectId }).first();
      if (project) {
        await db.projects.update(project, {
          tracks: cloneDeep(tracks.value),
          updatedAt: Date.now()
        });
        console.log('✅ AI分析结果已保存到数据库');
      }

      // 刷新播放器
      refreshPlayer();
    } catch (error) {
      console.error('❌ 保存AI分析结果失败:', error);
    }
  });
};





// 删除没有clip的轨道
const deleteEmptyTrack = () => {
  tracks.value = tracks.value.filter((track) => track.clips.length > 0);
};

const handleStopPlay = () => {
  playerRef.value?.handleStopPlay();
};

const refreshPlayer = () => {
  getThumbnail();
  playerRef.value?.refreshPlayer();
};

const handleAddClip = (clip: TrackClip) => {
  console.log('🎬 处理添加片段到播放器:', clip.name);
  nextTick(() => {
    playerRef.value?.addClip(clip);
  });
};

const handleExport = () => {
  playerRef.value?.handleExport();
};

// 🎯 验证和修复轨道数据的函数
const validateAndFixTrackData = () => {
  console.log('🔍 开始验证轨道数据...');

  let hasChanges = false;

  tracks.value.forEach((track, trackIndex) => {
    // 按开始时间排序片段
    const originalOrder = track.clips.map(clip => clip.id);
    track.clips.sort((a, b) => a.startTime - b.startTime);
    const newOrder = track.clips.map(clip => clip.id);

    if (JSON.stringify(originalOrder) !== JSON.stringify(newOrder)) {
      console.log(`🔧 轨道 ${trackIndex} 片段重新排序:`, {
        原顺序: originalOrder,
        新顺序: newOrder
      });
      hasChanges = true;
    }

    // 检查和修复时间重叠
    for (let i = 0; i < track.clips.length - 1; i++) {
      const currentClip = track.clips[i];
      const nextClip = track.clips[i + 1];

      if (currentClip.endTime > nextClip.startTime) {
        console.log(`🔧 修复轨道 ${trackIndex} 片段时间重叠:`, {
          当前片段: `${currentClip.name} (${currentClip.startTime}-${currentClip.endTime})`,
          下一片段: `${nextClip.name} (${nextClip.startTime}-${nextClip.endTime})`
        });

        // 调整下一片段的开始时间
        const timeDiff = nextClip.endTime - nextClip.startTime;
        nextClip.startTime = currentClip.endTime;
        nextClip.endTime = nextClip.startTime + timeDiff;
        hasChanges = true;
      }
    }

    // 检查分片后的片段是否在同一轨道
    const segmentGroups = new Map<string, TrackClip[]>();
    track.clips.forEach(clip => {
      // 检测分片片段的基础名称
      let baseName = clip.name;
      if (clip.name.includes('_1') || clip.name.includes('_2')) {
        baseName = clip.name.replace(/_[12]$/, '');
      } else if (clip.name.includes('(前半部分)') || clip.name.includes('(后半部分)')) {
        baseName = clip.name.replace(/\s*\([前后]半部分\)$/, '');
      }

      if (!segmentGroups.has(baseName)) {
        segmentGroups.set(baseName, []);
      }
      segmentGroups.get(baseName)!.push(clip);
    });

    // 检查是否有分片片段被错误分配到其他轨道
    segmentGroups.forEach((segments, baseName) => {
      if (segments.length > 1) {
        console.log(`🔍 发现分片组: ${baseName}，共 ${segments.length} 个片段`);
      }
    });
  });

  if (hasChanges) {
    console.log('✅ 轨道数据已修复');
    // 保存修复后的数据
    nextTick(() => {
      refreshPlayer();
    });
  } else {
    console.log('✅ 轨道数据验证通过，无需修复');
  }
};

// 视图切换方法
const switchToTraditionalView = () => {
  console.log('📊 切换到传统时间轴视图');

  // 🎯 修复：切换前验证和修复轨道数据
  validateAndFixTrackData();

  timelineViewMode.value = 'traditional';
  localStorage.setItem('timeline-view-mode', 'traditional');

  // 调试信息
  nextTick(() => {
    console.log('🔍 传统视图组件状态:', {
      timelineViewMode: timelineViewMode.value,
      clipTrackRef: !!clipTrackRef.value,
      tracks: tracks.value.length
    });
  });
};

const switchToCardsView = () => {
  console.log('🎬 切换到片段卡片视图');

  // 🎯 修复：切换前验证和修复轨道数据
  validateAndFixTrackData();

  timelineViewMode.value = 'cards';
  localStorage.setItem('timeline-view-mode', 'cards');
};

// 更新轨道数据的方法（供卡片视图使用）
const updateTracks = (newTracks: Track[]) => {
  const oldTrackCount = tracks.value.length;
  tracks.value = newTracks;
  const newTrackCount = newTracks.length;

  // 如果轨道数量发生变化，重新初始化垂直布局
  if (oldTrackCount !== newTrackCount) {
    console.log('🎬 轨道数量变化，重新调整布局:', {
      from: oldTrackCount,
      to: newTrackCount
    });
    nextTick(() => {
      setTimeout(() => {
        initializeVerticalSplit();
      }, 100);
    });
  }

  nextTick(() => {
    refreshPlayer();
  });
};

// 布局系统核心方法 - 重构版本
let horizontalSplitInstance: any = null;
let verticalSplitInstance: any = null;

// 面板切换处理方法（已废弃，左侧菜单栏始终可见）
const handleLeftMenuToggle = () => {
  console.log('🔄 左侧菜单切换（已废弃，菜单栏始终可见）');
  // leftMenuVisible.value = !leftMenuVisible.value;
  // savePanelStates();
  // reinitializeLayout();
};

const handleRightPanelToggle = () => {
  console.log('🔄 右侧属性面板切换');
  rightPanelVisible.value = !rightPanelVisible.value;
  savePanelStates();
  reinitializeLayout();
};

// 🎯 右侧面板拖拽调整功能
const startRightPanelResize = (event: MouseEvent) => {
  event.preventDefault();
  isResizingRightPanel = true;

  const startX = event.clientX;
  const startWidth = rightPanelWidth.value;

  console.log('🎯 开始调整右侧面板宽度', { startX, startWidth });

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizingRightPanel) return;

    // 计算新宽度（向左拖拽增加宽度，向右拖拽减少宽度）
    const deltaX = startX - e.clientX;
    const newWidth = startWidth + deltaX;

    // 限制宽度范围
    const clampedWidth = Math.max(minRightPanelWidth, Math.min(maxRightPanelWidth, newWidth));
    rightPanelWidth.value = clampedWidth;

    // 添加视觉反馈
    document.body.classList.add('resizing-right-panel');
  };

  const handleMouseUp = () => {
    if (!isResizingRightPanel) return;

    isResizingRightPanel = false;
    document.body.classList.remove('resizing-right-panel');

    console.log('✅ 右侧面板宽度调整完成', { newWidth: rightPanelWidth.value });

    // 保存宽度设置
    saveRightPanelWidth();

    // 移除事件监听器
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  // 添加事件监听器
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

// 保存右侧面板宽度到localStorage
const saveRightPanelWidth = () => {
  try {
    localStorage.setItem('rightPanelWidth', rightPanelWidth.value.toString());
  } catch (error) {
    console.warn('保存右侧面板宽度失败:', error);
  }
};

// 🎯 AI助手面板拖拽调整功能
const startAiPanelResize = (event: MouseEvent) => {
  event.preventDefault();
  event.stopPropagation();

  isResizingAiPanel = true;

  const startX = event.clientX;
  const startWidth = aiAssistantWidth.value;

  console.log('🎯 开始调整AI助手面板宽度', { startX, startWidth });

  // 添加拖拽开始的视觉反馈
  document.body.classList.add('resizing-ai-panel');

  // 创建拖拽遮罩，防止iframe等元素干扰
  const dragOverlay = document.createElement('div');
  dragOverlay.className = 'ai-drag-overlay';
  dragOverlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    cursor: col-resize;
    background: transparent;
  `;
  document.body.appendChild(dragOverlay);

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizingAiPanel) return;

    // 计算新宽度（向左拖拽增加宽度，向右拖拽减少宽度）
    const deltaX = startX - e.clientX;
    let newWidth = startWidth + deltaX;

    // 限制宽度范围
    newWidth = Math.max(minAiAssistantWidth, Math.min(maxAiAssistantWidth, newWidth));

    // 平滑的宽度更新
    const roundedWidth = Math.round(newWidth);
    if (Math.abs(roundedWidth - aiAssistantWidth.value) >= 1) {
      aiAssistantWidth.value = roundedWidth;

      // 实时更新布局
      updateLayoutImmediate();
    }
  };

  const handleMouseUp = (e: MouseEvent) => {
    if (!isResizingAiPanel) return;

    isResizingAiPanel = false;

    // 清理视觉反馈
    document.body.classList.remove('resizing-ai-panel');

    // 移除拖拽遮罩
    if (dragOverlay && dragOverlay.parentNode) {
      dragOverlay.parentNode.removeChild(dragOverlay);
    }

    console.log('✅ AI助手面板宽度调整完成', {
      newWidth: aiAssistantWidth.value,
      finalPosition: e.clientX
    });

    // 保存宽度设置
    saveAiAssistantWidth();

    // 移除事件监听器
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);

    // 延迟触发最终的布局更新
    nextTick(() => {
      setTimeout(() => {
        reinitializeLayout();
      }, 50);
    });
  };

  // 添加事件监听器
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

// 保存AI助手面板宽度到localStorage
const saveAiAssistantWidth = () => {
  try {
    localStorage.setItem('aiAssistantWidth', aiAssistantWidth.value.toString());
  } catch (error) {
    console.warn('保存AI助手面板宽度失败:', error);
  }
};

// 从localStorage恢复AI助手面板宽度
const restoreAiAssistantWidth = () => {
  try {
    const savedWidth = localStorage.getItem('aiAssistantWidth');
    if (savedWidth) {
      const width = parseInt(savedWidth, 10);
      if (width >= minAiAssistantWidth && width <= maxAiAssistantWidth) {
        aiAssistantWidth.value = width;
        console.log('✅ 恢复AI助手面板宽度:', width);
      }
    }
  } catch (error) {
    console.warn('恢复AI助手面板宽度失败:', error);
  }
};

// 恢复右侧面板宽度
const restoreRightPanelWidth = () => {
  try {
    const savedWidth = localStorage.getItem('rightPanelWidth');
    if (savedWidth) {
      const width = parseInt(savedWidth, 10);
      if (width >= minRightPanelWidth && width <= maxRightPanelWidth) {
        rightPanelWidth.value = width;
        console.log('📦 恢复右侧面板宽度:', width);
      }
    }
  } catch (error) {
    console.warn('恢复右侧面板宽度失败:', error);
  }
};

const handleCloseAIAssistant = () => {
  console.log('🔄 AI助手面板关闭，同步左侧菜单状态');
  aiAssistantVisible.value = false;
  savePanelStates();

  // 通知左侧菜单同步AI助手状态
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('updateAIAssistantState', {
      detail: { visible: false }
    }));
  }

  reinitializeLayout();
};

const handleToggleAIAssistantEvent = (event: CustomEvent) => {
  console.log('🔄 接收到来自菜单的AI助手切换事件:', event.detail.visible);
  aiAssistantVisible.value = event.detail.visible;
  savePanelStates();
  reinitializeLayout();
};

// 处理面板状态变化事件
const handlePanelStateChange = (event: CustomEvent) => {
  console.log('🔄 接收到面板状态变化事件:', event.detail);
  const { panelStates, activeTab } = event.detail;

  // 更新左侧面板状态
  Object.assign(leftPanelStates.value, {
    media: panelStates.media,
    audio: panelStates.audio,
    audioEffects: panelStates.audioEffects,
    text: panelStates.text,
    filter: panelStates.filter
  });

  // 更新当前显示的面板
  const hasActivePanel = Object.values(leftPanelStates.value).some(state => state);
  currentLeftPanel.value = hasActivePanel ?
    Object.keys(leftPanelStates.value).find(key =>
      leftPanelStates.value[key as keyof typeof leftPanelStates.value]
    ) || null : null;

  // 重要修复：左侧菜单栏始终保持可见，不受面板内容影响
  // leftMenuVisible 控制的是整个左侧菜单栏容器的可见性
  // 面板内容的显示/隐藏由 ClipMenu 组件内部的 hasActivePanel 控制
  // leftMenuVisible.value = true; // 始终保持菜单栏可见

  console.log('📦 面板状态更新:', {
    leftPanelStates: leftPanelStates.value,
    currentLeftPanel: currentLeftPanel.value,
    leftMenuVisible: leftMenuVisible.value,
    hasActivePanel: hasActivePanel
  });

  savePanelStates();
  reinitializeLayout();
};

// 🎯 处理左侧面板宽度变化事件
const handleLeftPanelWidthChange = (event: CustomEvent) => {
  console.log('🔄 接收到左侧面板宽度变化事件:', event.detail);
  const { width } = event.detail;

  if (typeof width === 'number' && width > 0) {
    leftPanelWidth.value = width;
    console.log('📏 更新左侧面板宽度:', width);

    // 触发布局重新计算
    reinitializeLayout();
  }
};

// 🔧 处理实时左侧面板宽度变化事件（拖拽过程中）
const handleLeftPanelWidthChangeImmediate = (event: CustomEvent) => {
  const { width } = event.detail;

  if (typeof width === 'number' && width > 0) {
    leftPanelWidth.value = width;

    // 🔧 拖拽过程中只更新宽度，不触发完整的布局重新计算
    // 这样可以避免拖拽过程中的卡顿和间隙
    nextTick(() => {
      // 仅更新必要的布局计算
      updateLayoutImmediate();
    });
  }
};

// 🔧 立即更新布局（用于拖拽过程中的实时响应）
const updateLayoutImmediate = () => {
  // 只更新关键的布局属性，避免复杂的重新计算
  const container = document.querySelector('#right') as HTMLElement;
  if (container) {
    const leftWidth = leftPanelWidth.value;
    const rightWidth = aiAssistantVisible.value ? aiAssistantWidth.value : 0;
    const availableWidth = window.innerWidth - leftWidth - rightWidth;

    // 直接设置样式，避免Vue的响应式更新延迟
    container.style.width = `${availableWidth}px`;
  }
};

// 保存面板状态到localStorage
const savePanelStates = () => {
  const panelStates = {
    leftMenuVisible: leftMenuVisible.value,
    rightPanelVisible: rightPanelVisible.value,
    rightPanelWidth: rightPanelWidth.value,
    aiAssistantVisible: aiAssistantVisible.value,
    aiAssistantWidth: aiAssistantWidth.value,
    timelineViewMode: timelineViewMode.value,
    leftPanelStates: leftPanelStates.value,
    currentLeftPanel: currentLeftPanel.value
  };
  localStorage.setItem(LAYOUT_STORAGE_KEY, JSON.stringify(panelStates));
  localStorage.setItem('ai-assistant-visible', JSON.stringify(aiAssistantVisible.value));
  localStorage.setItem('ai-assistant-width', aiAssistantWidth.value.toString());
  localStorage.setItem('timeline-view-mode', timelineViewMode.value);
};

// 从localStorage恢复面板状态
const restorePanelStates = () => {
  try {
    const savedStates = localStorage.getItem(LAYOUT_STORAGE_KEY);
    if (savedStates) {
      const states = JSON.parse(savedStates);
      // 左侧菜单栏始终保持可见
      leftMenuVisible.value = true;
      rightPanelVisible.value = states.rightPanelVisible ?? true;
      // 恢复右侧面板宽度
      if (states.rightPanelWidth && states.rightPanelWidth >= minRightPanelWidth && states.rightPanelWidth <= maxRightPanelWidth) {
        rightPanelWidth.value = states.rightPanelWidth;
      }
      aiAssistantVisible.value = states.aiAssistantVisible ?? true;
      // 恢复AI助手面板宽度
      if (states.aiAssistantWidth && states.aiAssistantWidth >= minAiAssistantWidth && states.aiAssistantWidth <= maxAiAssistantWidth) {
        aiAssistantWidth.value = states.aiAssistantWidth;
      }
      timelineViewMode.value = states.timelineViewMode ?? 'traditional';

      // 恢复左侧面板状态
      if (states.leftPanelStates) {
        Object.assign(leftPanelStates.value, states.leftPanelStates);
      }
      if (states.currentLeftPanel) {
        currentLeftPanel.value = states.currentLeftPanel;
      }
    }

    // 兼容旧版本的单独存储
    const savedAIState = localStorage.getItem('ai-assistant-visible');
    if (savedAIState !== null) {
      aiAssistantVisible.value = JSON.parse(savedAIState);
    }

    const savedViewMode = localStorage.getItem('timeline-view-mode');
    if (savedViewMode === 'cards' || savedViewMode === 'traditional') {
      timelineViewMode.value = savedViewMode;
    }

    console.log('📦 恢复面板状态完成:', {
      leftMenuVisible: leftMenuVisible.value,
      leftPanelStates: leftPanelStates.value,
      aiAssistantVisible: aiAssistantVisible.value
    });
  } catch (error) {
    console.warn('恢复面板状态失败:', error);
  }
};

// 重新初始化布局系统
const reinitializeLayout = () => {
  const hasActiveLeftPanel = Object.values(leftPanelStates.value).some(state => state);
  console.log('🔄 重新初始化布局系统', {
    leftMenu: leftMenuVisible.value,
    rightPanel: rightPanelVisible.value,
    aiAssistant: aiAssistantVisible.value,
    hasActiveLeftPanel: hasActiveLeftPanel,
    leftPanelStates: leftPanelStates.value,
    layoutConfig: layoutConfig.value
  });

  nextTick(() => {
    setTimeout(() => {
      destroyExistingSplits();
      initializeHorizontalSplit();
      initializeVerticalSplit();
    }, 150); // 增加延迟确保DOM更新完成
  });
};

// 处理窗口大小变化
const handleWindowResize = () => {
  // console.log('📐 窗口大小变化，重新调整布局');
  // 防抖处理，避免频繁重新初始化
  clearTimeout(resizeTimeout.value);
  resizeTimeout.value = setTimeout(() => {
    reinitializeLayout();
  }, 300);
};

// 防抖定时器
const resizeTimeout = ref<number | null>(null);

// 销毁现有的Split实例
const destroyExistingSplits = () => {
  if (horizontalSplitInstance) {
    horizontalSplitInstance.destroy();
    horizontalSplitInstance = null;
  }
  if (verticalSplitInstance) {
    verticalSplitInstance.destroy();
    verticalSplitInstance = null;
  }

  // 清除残留的gutter元素
  const existingGutters = document.querySelectorAll('.gutter');
  existingGutters.forEach(gutter => gutter.remove());
};

// 初始化水平分割布局
const initializeHorizontalSplit = () => {
  const config = layoutConfig.value;
  const sizes = config.getHorizontalSizes();
  const minSizes = config.getMinSizes();

  console.log('🔧 初始化水平分割:', {
    sizes,
    minSizes,
    leftActualWidth: config.leftWidth,
    hasActiveLeftPanel: Object.values(leftPanelStates.value).some(state => state)
  });

  try {
    horizontalSplitInstance = Split(['#left', '#right', '#aiPanel'], {
      sizes: sizes,
      minSize: minSizes,
      snapOffset: 30,
      gutterSize: 4,
      cursor: 'col-resize',
      onDragEnd: (newSizes) => {
        console.log('💾 保存水平分割尺寸:', newSizes);
        const savedSizes = localStorage.getItem(SPLIT_STORAGE_KEY);
        const sizeData = savedSizes ? JSON.parse(savedSizes) : {};
        localStorage.setItem(
          SPLIT_STORAGE_KEY,
          JSON.stringify({
            ...sizeData,
            horizontalSizes: newSizes,
          })
        );
      },
    });
  } catch (error) {
    console.error('水平分割初始化失败:', error);
  }
};

// 获取动态垂直分割配置 - 基于轨道数量和屏幕高度的智能调整
const getVerticalSplitConfig = () => {
  const screenHeight = window.innerHeight;
  const trackCount = tracks.value.length;

  // 基于轨道数量的基础配置 - 优化候选片段显示
  let baseConfig: { playerRatio: number; timelineRatio: number };
  if (trackCount <= 2) {
    // 0-2个轨道：减少播放器高度，为候选片段留出空间
    baseConfig = { playerRatio: 45, timelineRatio: 55 };
  } else if (trackCount <= 5) {
    // 3-5个轨道：适中的时间轴空间
    baseConfig = { playerRatio: 40, timelineRatio: 60 };
  } else {
    // 6个以上轨道：较大的时间轴空间
    baseConfig = { playerRatio: 35, timelineRatio: 65 };
  }

  // 根据屏幕高度微调
  if (screenHeight < 800) {
    // 小屏幕：给时间轴稍微多一点空间
    baseConfig.playerRatio -= 5;
    baseConfig.timelineRatio += 5;
    return {
      sizes: [baseConfig.playerRatio, baseConfig.timelineRatio],
      minSizes: [250, 300]
    };
  } else if (screenHeight < 1200) {
    // 中等屏幕：使用基础配置
    return {
      sizes: [baseConfig.playerRatio, baseConfig.timelineRatio],
      minSizes: [300, 350]
    };
  } else {
    // 大屏幕：可以给播放器稍微多一点空间
    baseConfig.playerRatio += 5;
    baseConfig.timelineRatio -= 5;
    return {
      sizes: [baseConfig.playerRatio, baseConfig.timelineRatio],
      minSizes: [350, 400]
    };
  }
};

// 初始化垂直分割布局
const initializeVerticalSplit = () => {
  try {
    const config = getVerticalSplitConfig();

    // 尝试从localStorage恢复用户自定义的分割比例
    const savedSizes = localStorage.getItem(SPLIT_STORAGE_KEY);
    let sizes = config.sizes;

    if (savedSizes) {
      try {
        const sizeData = JSON.parse(savedSizes);
        if (sizeData.verticalSizes && Array.isArray(sizeData.verticalSizes)) {
          sizes = sizeData.verticalSizes;
        }
      } catch (error) {
        console.warn('恢复垂直分割尺寸失败:', error);
      }
    }


    verticalSplitInstance = Split(['#top', '#bottom'], {
      direction: 'vertical',
      sizes: sizes,
      minSize: config.minSizes,
      snapOffset: 30,
      gutterSize: 4,
      cursor: 'row-resize',
      onDragEnd: (newSizes) => {
        console.log('💾 保存垂直分割尺寸:', newSizes);
        const savedSizes = localStorage.getItem(SPLIT_STORAGE_KEY);
        const sizeData = savedSizes ? JSON.parse(savedSizes) : {};
        localStorage.setItem(
          SPLIT_STORAGE_KEY,
          JSON.stringify({
            ...sizeData,
            verticalSizes: newSizes,
          })
        );
      },
    });
  } catch (error) {
    console.error('垂直分割初始化失败:', error);
  }
};

// 🎯 检查并加载视频处理结果
const checkAndLoadProcessingResult = async (projectId: string) => {
  try {
    console.log('🔍 检查视频处理结果:', projectId);

    // 🎯 优先检查 sessionStorage 中的处理结果（来自上传页面）
    const sessionData = sessionStorage.getItem('videoEditorData');
    console.log('📊 sessionStorage 检查结果:', {
      hasSessionData: !!sessionData,
      dataLength: sessionData?.length || 0,
      projectId: projectId
    });

    if (sessionData) {
      console.log('✅ 找到来自上传页面的处理结果');

      try {
        const editorData = JSON.parse(sessionData);
        console.log('📊 解析上传页面数据:', editorData);

        // 🎯 验证数据完整性
        if (!editorData.videoFile || !editorData.videoFile.name) {
          throw new Error('视频文件信息不完整');
        }

        console.log('📋 开始处理上传页面数据:', {
          fileName: editorData.videoFile.name,
          duration: editorData.videoFile.duration,
          hasProcessingResult: !!editorData.processingResult,
          hasSegments: !!(editorData.processingResult?.fusionResult?.adjustedSegments || editorData.processingResult?.segmentTimes)
        });

        // 🎯 首先确保视频文件已添加到素材库
        const mediaRecord = await ensureVideoInMediaLibrary(editorData.videoFile, editorData.processingResult);

        if (mediaRecord) {
          console.log('✅ 视频文件已成功添加到素材库');
          // 🎯 刷新素材库显示（如果有素材库组件的话）
          await refreshMediaLibrary();
        }

        // 转换为轨道数据
        const processedTracks = convertWebCodecsResultToTracks(editorData);

        if (processedTracks.length > 0) {
          tracks.value = processedTracks;

          // 保存到数据库
          await saveProject();

          // 🎯 更新历史记录
          const numericProjectId = getNumericProjectId(projectId);
          trackStore.clearHistory(numericProjectId);
          trackStore.saveHistoryState(numericProjectId, tracks.value);

          // 刷新播放器
          refreshPlayer();

          console.log('🎬 上传页面的视频处理结果已加载到轨道');

          // 🎯 显示详细的成功信息
          const segmentCount = processedTracks[0]?.clips?.length || 0;
          const message = segmentCount > 1
            ? `视频已自动加载，包含 ${segmentCount} 个智能分段`
            : '视频已自动加载到编辑器';

          ElMessage.success(message);

          // 🎯 调试信息：显示数据联动成功的详细信息
          console.log('✅ 数据联动成功完成:', {
            projectId: projectId,
            fileName: editorData.videoFile.name,
            trackCount: processedTracks.length,
            segmentCount: segmentCount,
            mediaLibraryUpdated: !!mediaRecord,
            processingMethod: editorData.metadata?.processingMethod,
            hasAudioTrack: processedTracks.some(track => track.clips.some(clip => clip.type === 'audio'))
          });

          // 清除 sessionStorage 数据，避免重复加载
          sessionStorage.removeItem('videoEditorData');
          return;
        } else {
          throw new Error('轨道数据转换失败');
        }
      } catch (parseError) {
        console.error('❌ 解析上传页面数据失败:', parseError);

        // 🎯 提供用户友好的错误提示
        ElMessage.error({
          message: '视频数据加载失败，请重新上传视频文件',
          duration: 5000,
          showClose: true
        });

        sessionStorage.removeItem('videoEditorData'); // 清除无效数据
      }
    }

    // 🔄 回退到原有的视频处理服务检查
    const result = videoProcessingService.getResult(projectId.toString());

    if (result) {
      console.log('✅ 找到处理结果，转换为轨道数据:', result);

      // 将处理结果转换为轨道数据
      const processedTracks = videoProcessingService.convertToTracks(result);

      if (processedTracks.length > 0) {
        tracks.value = processedTracks;

        // 保存到数据库
        await saveProject();

        // 更新历史记录
        trackStore.clearHistory(projectId);
        trackStore.saveHistoryState(projectId, tracks.value);

        // 刷新播放器
        refreshPlayer();

        console.log('🎬 视频处理结果已加载到轨道');
        ElMessage.success('视频处理完成，已自动加载到编辑器');
      }
    }
  } catch (error) {
    console.error('加载视频处理结果失败:', error);
  }
};

// 🎯 将 WebCodecs 处理结果转换为轨道数据
const convertWebCodecsResultToTracks = (editorData: any): Track[] => {
  try {
    console.log('🔄 转换 WebCodecs 处理结果为轨道数据:', editorData);

    const tracks: Track[] = [];
    const { videoFile, processingResult } = editorData;

    if (!processingResult) {
      throw new Error('没有处理结果数据');
    }

    // 🎯 详细的调试信息
    console.log('📊 处理结果分析:', {
      hasVideoFile: !!videoFile,
      videoFileName: videoFile?.name,
      videoDuration: videoFile?.duration,
      processingTotalTime: processingResult.totalTime,
      totalFrames: processingResult.totalFrames,
      hasFusionResult: !!processingResult.fusionResult,
      fusionBoundaries: processingResult.fusionResult?.adjustedBoundaries?.length || 0,
      hasSegmentTimes: !!processingResult.segmentTimes,
      segmentTimesLength: processingResult.segmentTimes?.length || 0,
      hasAsrResult: !!processingResult.asrResult,
      asrSentences: processingResult.asrResult?.sentences?.length || 0,
      // 🎯 添加更多诊断信息
      fusionResultStructure: processingResult.fusionResult ? Object.keys(processingResult.fusionResult) : [],
      segmentTimesData: processingResult.segmentTimes?.slice(0, 5) // 显示前5个时间点
    });

    // 🎯 直接创建完整视频片段，不使用任何分段数据
    console.log('📊 创建完整视频片段');
    const videoDuration = processingResult.videoDuration || processingResult.totalTime || videoFile.duration;

    // 🎯 调试视频时长信息
    console.log('🕐 视频时长调试信息:', {
      processingResultVideoDuration: processingResult.videoDuration,
      processingResultTotalTime: processingResult.totalTime,
      videoFileDuration: videoFile.duration,
      finalVideoDuration: videoDuration
    });

    const segments = [{
      start: 0,
      end: videoDuration,
      confidence: 1.0,
      reason: '完整视频'
    }];

    // 🎯 创建完整视频轨道
    const filePath = videoFile.filePath || `/video/${videoFile.name}`;
    const segment = segments[0]; // 只有一个完整片段
    const duration = segment.end - segment.start;

    // 🎯 调试片段创建信息
    console.log('🎬 创建视频片段详细信息:', {
      segmentStart: segment.start,
      segmentEnd: segment.end,
      calculatedDuration: duration,
      videoFileName: videoFile.name,
      videoFileDuration: videoFile.duration,
      filePath: filePath
    });

    const videoClip: TrackClip = {
      id: 'full_video_clip',
      name: videoFile.name,
      type: 'video' as MediaType,
      startTime: 0,
      endTime: duration,
      duration: duration,
      originalDuration: duration,
      sourceStartTime: 0,
      sourceEndTime: duration,
      path: filePath,
      originalPath: videoFile.name,
      thumbnail: [],
      volume: 1,
      // 🎯 添加文件信息，确保播放器能正确加载
      fileInfo: {
        name: videoFile.name,
        size: videoFile.size,
        type: videoFile.type,
        duration: videoFile.duration
      }
    };

    tracks.push({
      id: 'video_track_1',
      clips: [videoClip]
    });

    console.log(`✅ 创建完整视频轨道，时长: ${duration}秒`);





    console.log(`🎯 转换完成，生成 ${tracks.length} 个轨道`);
    return tracks;

  } catch (error) {
    console.error('❌ 转换 WebCodecs 结果失败:', error);
    throw error; // 🎯 不使用降级方案，直接抛出错误
  }
};



// 🎯 刷新素材库显示
const refreshMediaLibrary = async () => {
  try {
    // 重新加载素材库数据
    const allMedias = await db.medias.orderBy('createTime').reverse().toArray();
    console.log('📚 素材库已刷新，共', allMedias.length, '个文件');

    // 如果有素材库组件，可以在这里触发刷新
    // 这里可以发送事件或调用素材库组件的刷新方法

    return allMedias;
  } catch (error) {
    console.error('❌ 刷新素材库失败:', error);
    return [];
  }
};

// 🎯 确保视频文件已添加到素材库
const ensureVideoInMediaLibrary = async (videoFile: any, processingResult: any) => {
  try {
    console.log('📚 检查视频文件是否在素材库中:', videoFile.name);

    // 检查文件是否已存在于数据库中
    const existingMedia = await db.medias.where({ name: videoFile.name }).first();

    if (existingMedia) {
      console.log('✅ 视频文件已存在于素材库中');
      return existingMedia;
    }

    console.log('📝 视频文件不存在，开始添加到素材库...');

    // 🎯 验证文件是否在 OPFS 中存在
    const filePath = videoFile.filePath || `/video/${videoFile.name}`;
    let fileExists = false;

    try {
      // 尝试访问 OPFS 中的文件
      const opfsRoot = await navigator.storage.getDirectory();
      const videoDir = await opfsRoot.getDirectoryHandle('video', { create: false });
      const fileHandle = await videoDir.getFileHandle(videoFile.name, { create: false });
      const file = await fileHandle.getFile();
      fileExists = file.size > 0;
      console.log('✅ 文件在 OPFS 中存在，大小:', file.size);
    } catch (error) {
      console.warn('⚠️ 无法在 OPFS 中找到文件，可能需要重新上传:', error);
      // 文件不存在，但我们仍然可以创建记录，用户稍后可以重新上传
    }

    // 确定文件类型
    const fileExtension = videoFile.name.split('.').pop()?.toLowerCase();
    const fileType = ['mp4', 'mov', 'avi', 'mkv', 'webm'].includes(fileExtension || '') ? 'video' :
                     ['mp3', 'wav', 'aac', 'm4a'].includes(fileExtension || '') ? 'audio' : 'video';

    // 🎯 创建增强的媒体记录
    const mediaRecord = {
      name: videoFile.name,
      size: videoFile.size || 0,
      type: fileType as 'video' | 'audio' | 'image',
      path: filePath,
      duration: videoFile.duration ? Math.round(videoFile.duration * 1000000) : undefined, // 转换为微秒
      createTime: new Date().getTime(),
      isAnimateImg: false,
      // 🎯 添加处理结果相关信息
      metadata: {
        fromUploadPage: true,
        processingTimestamp: processingResult?.processingTimestamp,
        hasSegments: !!(processingResult?.fusionResult?.adjustedSegments || processingResult?.segmentTimes),
        segmentCount: processingResult?.fusionResult?.adjustedSegments?.length || 0,
        fileExists: fileExists
      }
    };

    // 添加到数据库
    const mediaId = await db.medias.add(mediaRecord);
    console.log('✅ 视频文件已添加到素材库，ID:', mediaId);

    return { ...mediaRecord, id: mediaId };

  } catch (error) {
    console.error('❌ 添加视频文件到素材库失败:', error);
    // 不抛出错误，允许继续处理轨道数据
    return null;
  }
};
</script>

<style lang="scss">
/* 现代化剪辑页面样式 - 紫色主题 */

/* 专业视频编辑器样式变量 */
:root {
  --editor-bg-primary: #1a1a1a;
  --editor-bg-secondary: #2a2a2a;
  --editor-bg-tertiary: #333333;
  --editor-panel-bg: #252525;
  --editor-border: #404040;
  --editor-border-light: #505050;
  --editor-text-primary: #ffffff;
  --editor-text-secondary: #b3b3b3;
  --editor-text-muted: #808080;
  --editor-accent: #0ea5e9;
  --editor-accent-hover: #0284c7;
  --editor-success: #10b981;
  --editor-warning: #f59e0b;
  --editor-error: #ef4444;
  --editor-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  --editor-radius: 4px;
  --editor-transition: 0.2s ease;
}

/* 专业编辑器主容器 */
.editor-main-container {
  background: var(--editor-bg-primary);
  color: var(--editor-text-primary);
}

/* 专业编辑器面板 */
.editor-panel {
  background: var(--editor-panel-bg);
  border-right: 1px solid var(--editor-border);
  transition: all var(--editor-transition);
}

.editor-panel-right {
  border-right: none;
  border-left: 1px solid var(--editor-border);
}

/* Split.js 分割线样式 - 专业编辑器风格 */
.gutter {
  background: var(--editor-border);
  transition: background-color var(--editor-transition);
}

.gutter:hover {
  background: var(--editor-accent);
}

.gutter.gutter-horizontal {
  cursor: col-resize;
  width: 4px !important;
}

.gutter.gutter-vertical {
  cursor: row-resize;
  height: 4px !important;
}

/* 编辑器面板头部 */
.editor-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--editor-bg-secondary);
  border-bottom: 1px solid var(--editor-border);
  min-height: 48px;
}

.editor-panel-title {
  color: var(--editor-text-primary);
  font-size: 14px;
  font-weight: 500;
}

.editor-panel-badge {
  background: var(--editor-bg-tertiary);
  color: var(--editor-text-secondary);
  padding: 2px 8px;
  border-radius: var(--editor-radius);
  font-size: 11px;
  font-family: monospace;
}

.editor-close-btn {
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  color: var(--editor-text-secondary);
  cursor: pointer;
  border-radius: var(--editor-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all var(--editor-transition);
}

.editor-close-btn:hover {
  background: var(--editor-error);
  color: white;
}

/* 编辑器工具栏 */
.editor-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: var(--editor-bg-secondary);
  border-bottom: 1px solid var(--editor-border);
  min-height: 40px;
}

.editor-view-switcher {
  display: flex;
  background: var(--editor-bg-tertiary);
  border-radius: var(--editor-radius);
  padding: 2px;
}

.editor-view-btn {
  padding: 6px 12px;
  background: transparent;
  border: none;
  color: var(--editor-text-secondary);
  cursor: pointer;
  border-radius: var(--editor-radius);
  font-size: 13px;
  transition: all var(--editor-transition);
}

.editor-view-btn:hover {
  background: var(--editor-bg-secondary);
  color: var(--editor-text-primary);
}

.editor-view-btn.active {
  background: var(--editor-accent);
  color: white;
}

/* 左侧面板容器 */
.left-panel-container {
  transition: all var(--editor-transition);
}

/* 简洁的面板过渡 */
#left, #right, #aiPanel {
  transition: width var(--editor-transition);
}

/* 响应式布局优化 */
.layout-panel {
  min-width: 0;
  overflow: hidden;
}

/* 专业编辑器基础交互 */
button {
  transition: background-color var(--editor-transition);
}

button:hover {
  background: var(--editor-bg-tertiary);
}

input, textarea, select {
  background: var(--editor-bg-tertiary);
  border: 1px solid var(--editor-border);
  color: var(--editor-text-primary);
  transition: border-color var(--editor-transition);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--editor-accent);
}

/* 专业编辑器文本样式 */
h1, h2, h3, h4, h5, h6 {
  color: var(--editor-text-primary);
  margin: 0;
}

.text-primary {
  color: var(--editor-text-primary);
}

.text-secondary {
  color: var(--editor-text-secondary);
}

.text-muted {
  color: var(--editor-text-muted);
}

/* 状态指示器 */
.status-success {
  color: var(--editor-success);
}

.status-warning {
  color: var(--editor-warning);
}

.status-error {
  color: var(--editor-error);
}

/* 移除过度复杂的样式，保持专业简洁 */

.view-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-text {
  color: #e5e7eb;

  /* 使用统一字体系统 */
  font-family: var(--font-family-primary);
  font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
}

.compatibility-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #065f46;
  border-radius: 6px;
}

.badge-icon {
  font-size: var(--text-sm);
}

.badge-text {
  color: #d1fae5;

  /* 使用统一字体系统 */
  font-family: var(--font-family-primary);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-wide);
}

.timeline-view-container {
  flex: 1;
  overflow: hidden;
  background: #201f20;
  height: 100%; /* 恢复到100%高度 */
  min-height: 400px; /* 确保最小高度 */
}

/* 确保底部容器使用全部可用高度 */
#bottom {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 确保时间轴刻度容器正确显示 */
.timeline-view-container :deep(.ticks-container) {
  position: relative;
  background: #2a2a2a;
  border-bottom: 1px solid #444;
  z-index: 5;
}

/* 专业编辑器拖拽手柄 */
.right-panel-resize-handle {
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  cursor: col-resize;
  background: var(--editor-border);
  transition: background-color var(--editor-transition);
  z-index: 10;
}

.right-panel-resize-handle:hover {
  background: var(--editor-accent);
}

/* 移除复杂装饰，保持专业简洁 */

/* 面板宽度指示器 */
.panel-width-indicator {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  padding: 2px 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  color: #60a5fa;
}

/* 关闭按钮样式 */
.panel-close-btn {
  transition: all 0.2s ease;
  border-radius: 4px;
}

.panel-close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* 🔧 左侧面板容器优化样式 */
.left-panel-container {
  /* 确保面板有明确的定位 */
  position: relative;
  /* 防止内容溢出 */
  overflow: hidden;
  /* 使用硬件加速 */
  transform: translateZ(0);
  will-change: width;
  /* 确保面板始终贴合左边 */
  left: 0;
  top: 0;
  /* 默认的平滑过渡 */
  transition: width 0.3s ease-in-out;
  /* 确保z-index正确 */
  z-index: 10;
}

/* 🔧 拖拽时禁用过渡效果 */
body.resizing-left-panel .left-panel-container {
  transition: none !important;
}

/* 🔧 右侧内容区域优化 */
#right {
  /* 确保右侧区域正确填充剩余空间 */
  flex: 1;
  min-width: 0; /* 允许flex项目收缩 */
  /* 防止内容溢出 */
  overflow: hidden;
  /* 确保正确的定位 */
  position: relative;
  /* 使用硬件加速 */
  transform: translateZ(0);
  will-change: width;
}

/* 拖拽时的全局样式 */
body.resizing-right-panel {
  cursor: col-resize !important;
  user-select: none !important;
}

body.resizing-right-panel * {
  cursor: col-resize !important;
  user-select: none !important;
}

/* 🔧 左侧面板拖拽时的全局样式 */
body.resizing-left-panel {
  cursor: col-resize !important;
  user-select: none !important;
}

body.resizing-left-panel * {
  cursor: col-resize !important;
  user-select: none !important;
}

/* 🎯 AI助手面板拖拽分隔条样式 */
.ai-resize-handle {
  position: absolute;
  left: -4px;
  top: 0;
  bottom: 0;
  width: 8px;
  cursor: col-resize;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.ai-resize-handle:hover {
  background: rgba(79, 70, 229, 0.1);
}

.ai-resize-handle:hover .ai-resize-indicator {
  background: #4f46e5;
  box-shadow: 0 0 8px rgba(79, 70, 229, 0.3);
}

.ai-resize-indicator {
  width: 2px;
  height: 40px;
  background: #6b7280;
  border-radius: 1px;
  transition: all 0.2s ease;
}

/* AI助手面板拖拽时的全局样式 */
body.resizing-ai-panel {
  cursor: col-resize !important;
  user-select: none !important;
}

body.resizing-ai-panel * {
  cursor: col-resize !important;
  user-select: none !important;
}

/* AI助手面板过渡效果 */
#rightPanel {
  transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 拖拽时禁用过渡效果 */
body.resizing-ai-panel #rightPanel {
  transition: none !important;
}
</style>
