<!--
  * 视频播放器组件
  * 实现了视频播放、暂停、帧进、帧退、全屏等功能
  * 使用canvas进行视频渲染，支持实时预览编辑效果
-->
<template>
  <!-- 播放器容器：包含加载状态 -->
  <div
    ref="playerRef"
    class="player-container w-full h-full bg-[#1b1b1b] relative flex flex-col"
    v-loading="loading"
  >
    <!-- 视频画面显示区域：居中显示视频内容 -->
    <div class="flex-1 flex items-center justify-center relative">
      <!-- 空状态占位符：当没有视频内容时显示 -->
      <div
        v-if="!hasVideoContent"
        class="absolute inset-0 flex items-center justify-center bg-[#1b1b1b] text-gray-400 text-lg"
      >
        <div class="text-center">
          <div class="mb-2">📹</div>
          <div>将视频拖拽到时间轴开始编辑</div>
        </div>
      </div>

      <!-- 视频渲染画布：使用canvas进行视频帧渲染 -->
      <div
        ref="avCanvas"
        class="relative z-10"
        :class="{ 'opacity-0': !hasVideoContent }"
        :style="{
          width: canvasSize.width + 'px',
          height: canvasSize.height + 'px',
        }"
      ></div>
    </div>

    <!-- 播放器控制栏：包含播放控制和时间显示 -->
    <div class="control-bar w-full px-3 py-2 flex items-center justify-between bg-gray-800 border-t border-gray-600">
      <!-- 左侧：时间显示区域 -->
      <div class="flex items-center gap-2 text-white text-sm">
        <span>{{ formatTime(currentTime) }}</span>
        <span>/</span>
        <span>{{ formatTime(playerDuration) }}</span>
      </div>

      <!-- 中间：播放控制按钮组 -->
      <div class="flex items-center gap-4">
        <!-- 上一帧按钮：逐帧后退 -->
        <button
          class="text-white hover:text-purple-500 transition-colors p-2 rounded bg-gray-700 hover:bg-gray-600"
          @click="prevFrame"
          title="上一帧"
        >
          ⏮
        </button>

        <!-- 播放/暂停切换按钮 -->
        <button
          class="text-white hover:text-purple-500 transition-colors p-3 rounded-full bg-purple-600 hover:bg-purple-700"
          @click="() => { console.log('🎯 播放按钮被点击!', !isPlaying); handlePlay(!isPlaying); }"
          title="播放/暂停"
        >
          {{ isPlaying ? '⏸' : '▶' }}
        </button>

        <!-- 下一帧按钮：逐帧前进 -->
        <button
          class="text-white hover:text-purple-500 transition-colors p-2 rounded bg-gray-700 hover:bg-gray-600"
          @click="nextFrame"
          title="下一帧"
        >
          ⏭
        </button>
      </div>

      <!-- 右侧：功能按钮组 -->
      <div class="flex items-center gap-3">
        <!-- 全屏切换按钮 -->
        <button
          class="text-white hover:text-purple-500 transition-colors p-2 rounded bg-gray-700 hover:bg-gray-600"
          @click="toggleFullscreen"
          title="全屏"
        >
          {{ isFullscreen ? '🗗' : '🗖' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Vue APIs are auto-imported by unplugin-auto-import
import { AVCanvas } from '@webav/av-canvas';
import { MP4Clip, AudioClip, VisibleSprite, ImgClip } from '@webav/av-cliper';
import { Log } from '@webav/internal-utils';
import { useTrackStore } from '@/store/modules/track';
import { TextClip } from '@/components/av-cliper/clips/text-clip';
import { FilterClip } from '@/components/av-cliper/clips/filter-clip';
import { ElMessageBox, ElMessage } from 'element-plus';
import type { ElMessageBoxOptions } from 'element-plus';
import type { Track, TrackClip, FilterTrackClip } from '@/types/track';
import { getFile, createFileWriter } from '@/utils/opfs-file';
import { opfsStorageManager } from '@/utils/opfs-storage-manager';
import { emergencyStorageCleanup } from '@/utils/emergency-storage-cleanup';
import { OPFSDebugger } from '@/utils/opfs-debug';
import { validateWebAVFile, generateWebAVErrorReport } from '@/utils/webav-file-validator';
import * as PIXI from 'pixi.js';
import { Icon } from '@iconify/vue';
import { useRouter } from 'vue-router';

// Props & Emits
const props = defineProps<{
  tracks: Track[];
  currentTime: number;
  playerDuration: number;
}>();

const emit = defineEmits([
  'prevFrame',
  'nextFrame',
  'timeUpdate',
  'captureImage',
  'fullscreenChange',
  'updateClipProps',
]);

// Store & Inject
const trackStore = useTrackStore();
const injectActiveClip = inject('activeClip') as
  | ((id: string) => void)
  | undefined;

// 状态变量
const loading = ref(false);
const isPlaying = ref(false);
const loadingTimeout = ref<NodeJS.Timeout | null>(null);
const hasVideoContent = ref(false); // 新增：是否有视频内容

const isFullscreen = ref(false);
const avCanvas = ref(null);
const playerRef = ref<HTMLElement | null>(null);
const initCount = ref(0);
let cvs = null;

// 计算属性
const canvasSize = computed(() => trackStore.getCanvasSize);
const initTotal = computed(() => {
  let total = 0;
  props.tracks.forEach((track) => {
    track.clips.forEach((clip) => {
      total++;
    });
  });
  return total;
});

// 检查是否有视频内容的方法
const updateVideoContentStatus = () => {
  let hasVideo = false;

  // 检查所有轨道中是否有视频片段
  for (const track of props.tracks) {
    for (const clip of track.clips) {
      if (clip.type === 'video' || clip.type === 'image') {
        hasVideo = true;
        break;
      }
    }
    if (hasVideo) break;
  }

  console.log('🎬 更新视频内容状态:', hasVideo);
  hasVideoContent.value = hasVideo;
};

// 监听 tracks 变化，自动更新视频内容状态
watch(
  () => props.tracks,
  () => {
    updateVideoContentStatus();
  },
  { deep: true, immediate: true }
);

// 播放器核心方法
const handlePlay = (isPlay: boolean) => {
  console.log('🎯 播放器 handlePlay 被调用:', isPlay);
  console.log('📊 当前播放器状态:', {
    isPlaying: isPlaying.value,
    currentTime: props.currentTime,
    playerDuration: props.playerDuration,
    cvsExists: !!cvs
  });

  if (isPlay) {
    if (cvs) {
      const startTimeMicros = props.currentTime * 1e6;
      console.log(`▶️ 开始播放: ${props.currentTime}s (${startTimeMicros}μs)`);

      // 检查当前时间下有哪些Sprite应该可见
      console.log(`🔍 播放时Sprite状态检查:`);
      sprMap.forEach((sprite, clipId) => {
        if (sprite && sprite.time) {
          const isInRange = props.currentTime >= (sprite.time.offset / 1e6) &&
                           props.currentTime < ((sprite.time.offset + sprite.time.duration) / 1e6);
          console.log(`  - ${clipId}: 时间范围 ${(sprite.time.offset / 1e6).toFixed(2)}s - ${((sprite.time.offset + sprite.time.duration) / 1e6).toFixed(2)}s, 当前可见: ${isInRange}`);
        } else {
          console.warn(`⚠️ 无效的Sprite: ${clipId}`, sprite);
        }
      });

      cvs.play({ start: startTimeMicros });
      isPlaying.value = true;
      console.log('✅ 播放器开始播放');
    } else {
      console.error('❌ Canvas 未初始化，无法播放');
    }
  } else {
    if (cvs) {
      cvs.pause();
      isPlaying.value = false;
      console.log('⏸️ 播放器暂停');
    } else {
      console.error('❌ Canvas 未初始化，无法暂停');
    }
  }
};

const handleStopPlay = () => {
  cvs.pause();
  isPlaying.value = false;
};



const prevFrame = () => {
  console.log('⏮️ 上一帧按钮被点击');
  if (isPlaying.value) {
    handlePlay(false);
  }
  emit('timeUpdate', props.currentTime - 1 / 30);
  cvs.previewFrame(props.currentTime * 1e6);
};

const nextFrame = () => {
  console.log('⏭️ 下一帧按钮被点击');
  if (isPlaying.value) {
    handlePlay(false);
  }
  emit('timeUpdate', props.currentTime + 1 / 30);
  cvs.previewFrame(props.currentTime * 1e6);
};

// 全屏控制 - 复制自PlayerComponentOptimized.vue的完整实现
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  emit('fullscreen', isFullscreen.value);

  if (isFullscreen.value) {
    if (playerRef.value?.requestFullscreen) {
      playerRef.value.requestFullscreen();
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  }
  console.log(isFullscreen.value ? '🔍 进入全屏' : '🔍 退出全屏');
};

// 键盘控制相关
const handleKeyPress = (e: KeyboardEvent) => {
  const activeElement = document.activeElement;
  const isInput =
    activeElement instanceof HTMLInputElement ||
    activeElement instanceof HTMLTextAreaElement ||
    activeElement?.getAttribute('contenteditable') === 'true';

  if (!isInput) {
    switch (e.code) {
      case 'Space':
        e.preventDefault();
        handlePlay(!isPlaying.value);
        break;
      case 'ArrowLeft':
        e.preventDefault();
        prevFrame();
        break;
      case 'ArrowRight':
        e.preventDefault();
        nextFrame();
        break;
      case 'KeyF':
        e.preventDefault();
        toggleFullscreen();
        break;
    }
  }
};

// Canvas 初始化和管理
const initCanvas = () => {
  cvs = new AVCanvas(avCanvas.value, {
    bgColor: 'transparent', // 改为透明背景，让占位符可见
    width: 1920,
    height: 1080,
  });
  console.log(cvs);
  initClips();
  cvs.previewFrame(0);

  cvs.on('timeupdate', (time: number) => {
    emit('timeUpdate', time / 1e6);
  });
  cvs.on('playing', () => {
    console.log('playing');
  });
  cvs.on('paused', () => {
    console.log('paused');
    if (props.currentTime >= props.playerDuration) {
      isPlaying.value = false;
      emit('timeUpdate', 0);
    }
  });
  cvs.on('activeSpriteChange', (sprite: VisibleSprite | null) => {
    for (const [key, spr] of sprMap.entries()) {
      if (Object.is(spr, sprite)) {
        injectActiveClip?.(key);
      }
    }
  });

  // 初始化完成后更新视频内容状态
  updateVideoContentStatus();
};

// Clip 管理相关方法
const sprMap = new Map<string, VisibleSprite>();
const initializingClips = new Set<string>();

// 共享视频文件管理 - 原版方案
const sharedVideoClips = new Map<string, MP4Clip>();



const initClips = () => {
  props.tracks.forEach((track) => {
    track.clips.forEach((clip) => {
      initClip(clip);
    });
  });
};



const addClip = (clip: TrackClip) => {
  initClip(clip);
  // 添加片段后更新视频内容状态
  updateVideoContentStatus();
};

const activeClip = (clip: TrackClip | null) => {
  if (!clip) return (cvs.activeSprite = null);
  const spr = sprMap.get(clip.id);
  if (cvs && spr?.visible) {
    cvs.activeSprite = spr;
  }
};

const initClip = async (clip: TrackClip, retryCount = 0) => {
  const maxRetries = 2; // 最大重试次数

  // 检查是否已经在初始化或已完成
  if (sprMap.has(clip.id)) {
    console.log(`⚠️ 片段已存在，跳过初始化: ${clip.name}`);
    return;
  }

  // 防止重复初始化
  if (initializingClips.has(clip.id)) {
    console.log(`⚠️ 片段正在初始化中，跳过重复请求: ${clip.name}`);
    return;
  }

  // 设置初始化标记
  initializingClips.add(clip.id);

  let spr: VisibleSprite = null;

  try {
    console.log(`🔄 开始初始化片段: ${clip.name} (${clip.type})`);

    // 创建超时Promise
    const createTimeoutPromise = (timeout: number, operation: string) => {
      return new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`${operation}超时 (${timeout}ms)`)), timeout);
      });
    };

    // 使用OPFS存储管理器
    const checkStorageQuota = () => opfsStorageManager.hasEnoughSpace(90);
    const cleanupTempFiles = () => opfsStorageManager.cleanup();

    // 文件验证函数
    const validateVideoFile = async (standardFile: any, clipName: string) => {
      // 检查文件大小（如果可用）
      if (standardFile.size !== undefined) {
        if (standardFile.size === 0) {
          throw new Error(`文件为空: ${clipName}`);
        }

        if (standardFile.size < 1024) { // 小于1KB可能是损坏的文件
          throw new Error(`文件过小，可能已损坏: ${clipName} (${standardFile.size} bytes)`);
        }
      }

      console.log(`📊 文件基本信息: ${clipName}`, {
        size: standardFile.size || 'unknown',
        type: standardFile.type || 'unknown',
        hasStream: typeof standardFile.stream === 'function',
        lastModified: standardFile.lastModified ? new Date(standardFile.lastModified).toISOString() : 'unknown'
      });

      // 检查文件头（如果支持stream）
      if (typeof standardFile.stream === 'function') {
        try {
          const stream = await standardFile.stream();
          const reader = stream.getReader();
          const { value, done } = await reader.read();
          reader.releaseLock();

          if (done || !value || value.length < 8) {
            throw new Error(`无法读取文件头: ${clipName} - 文件流为空或过短`);
          }

          console.log(`📊 文件头信息: ${clipName}, 头部长度: ${value.length} bytes`);

        // 检查MP4文件头标识
        const header = new Uint8Array(value.slice(0, 8));
        const hasValidHeader = (
          // ftyp box
          (header[4] === 0x66 && header[5] === 0x74 && header[6] === 0x79 && header[7] === 0x70) ||
          // moov box
          (header[4] === 0x6D && header[5] === 0x6F && header[6] === 0x6F && header[7] === 0x76)
        );

        if (!hasValidHeader) {
          console.warn(`⚠️ 文件头验证失败，但继续尝试处理: ${clipName}`);
        } else {
          console.log(`✅ 文件头验证通过: ${clipName}`);
        }

        } catch (error) {
          console.warn(`⚠️ 文件头验证异常，但继续尝试处理: ${clipName}`, error);
        }
      } else {
        console.log(`ℹ️ 文件对象不支持stream，跳过文件头验证: ${clipName}`);
      }
    };

    // MP4Clip创建验证函数 - 基于 WebAV 最佳实践
    const createMP4ClipWithValidation = async (source: any, clipName: string, clipPath: string) => {
      return new Promise(async (resolve, reject) => {
        try {
          // 验证源数据是否有效
          if (!source) {
            reject(new Error(`无效的源数据: ${clipName} (路径: ${clipPath}) - 可能是文件不存在或损坏`));
            return;
          }

          console.log(`🎬 WebAV: 开始创建MP4Clip: ${clipName} (路径: ${clipPath})`);
          console.log(`📊 WebAV: 源数据类型分析:`, {
            constructorName: source.constructor?.name || 'unknown',
            hasStream: 'stream' in source,
            hasFile: 'file' in source,
            hasSize: 'size' in source,
            isReadableStream: source instanceof ReadableStream,
            isOTFile: source.constructor?.name?.includes('H') || false
          });

          // WebAV 推荐方式：直接传递源数据，让 WebAV 自动处理
          const mp4Clip = new MP4Clip(source);
          console.log(`🎥 WebAV: MP4Clip实例创建完成，等待ready事件`);

          // WebAV 错误处理：监听可能的错误
          let isResolved = false;
          let readyTimeout: NodeJS.Timeout;

          const cleanup = () => {
            if (readyTimeout) clearTimeout(readyTimeout);
          };

          // 设置超时检测
          readyTimeout = setTimeout(() => {
            if (!isResolved) {
              isResolved = true;
              cleanup();
              reject(new Error(`MP4Clip ready事件超时: ${clipName} (路径: ${clipPath}) - 可能是文件格式不支持或损坏`));
            }
          }, 30000);

          // 监听ready事件
          mp4Clip.ready.then((result) => {
            if (!isResolved) {
              isResolved = true;
              cleanup();
              console.log(`🎯 MP4Clip ready事件触发: ${clipName}`, result);

              // 验证ready结果
              if (result && result.duration > 0) {
                resolve(mp4Clip);
              } else {
                reject(new Error(`MP4Clip ready返回无效结果: ${clipName} (路径: ${clipPath}) - duration: ${result?.duration}`));
              }
            }
          }).catch((error) => {
            if (!isResolved) {
              isResolved = true;
              cleanup();
              console.error(`❌ MP4Clip ready失败: ${clipName} (路径: ${clipPath})`, error);

              // 根据错误类型提供更具体的信息
              let errorMsg = `MP4Clip ready失败: ${error.message}`;
              if (error.message.includes('stream is done')) {
                errorMsg += ` - 这通常表示文件不存在、损坏或格式不支持`;
              }

              reject(new Error(errorMsg));
            }
          });

        } catch (error) {
          console.error(`❌ MP4Clip构造失败: ${clipName} (路径: ${clipPath})`, error);
          reject(new Error(`MP4Clip构造失败: ${clipName} (路径: ${clipPath}) - ${error.message}`));
        }
      });
    };

    // 根据类型创建不同的 clip 和 sprite
    switch (clip.type) {
      case 'video': {
        console.log(`📁 获取视频文件: ${clip.path}`);
        console.log(`🔍 片段详细信息:`, {
          clipId: clip.id,
          clipName: clip.name,
          clipPath: clip.path,
          clipType: clip.type,
          clipDuration: clip.duration,
          startTime: clip.startTime,
          endTime: clip.endTime
        });

        // 检查存储空间
        const hasEnoughSpace = await checkStorageQuota();
        if (!hasEnoughSpace) {
          console.log('🧹 存储空间不足，尝试强制清理...');

          // 使用紧急清理工具
          console.log('🚨 启动紧急存储清理...');
          const emergencySuccess = await emergencyStorageCleanup.emergencyCleanup();

          if (!emergencySuccess) {
            // 如果紧急清理失败，尝试常规清理
            for (let i = 0; i < 3; i++) {
              await cleanupTempFiles();
              await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒

              const spaceAfterCleanup = await checkStorageQuota();
              if (spaceAfterCleanup) {
                console.log(`✅ 第${i + 1}次清理后存储空间恢复`);
                break;
              }
            }
          }

          // 最终检查
          const finalSpaceCheck = await checkStorageQuota();
          if (!finalSpaceCheck) {
            // 如果是分段文件路径错误，提供特殊处理
            if (clip.path === '/video/6月5日-1.mov' && clip.id.includes('_seg_')) {
              throw new Error(`分段文件路径错误: ${clip.name} - 后端应该生成独立的分段文件路径，而不是使用原始文件路径`);
            }
            throw new Error('存储空间严重不足，请清理浏览器数据或联系管理员');
          }
        }

        // WebAV 文件验证和获取
        console.log(`🔍 WebAV: 开始文件验证: ${clip.path}`);
        console.log(`🔍 WebAV: 片段类型: ${clip.id.includes('_seg_') ? '分段片段' : '普通片段'}`);

        const validationResult = await validateWebAVFile(
          clip.path,
          clip.name,
          clip.id.includes('_seg_')
        );

        if (!validationResult.isValid) {
          const errorReport = generateWebAVErrorReport(clip.name, clip.path, validationResult.error || 'Unknown error');
          console.error(errorReport);
          throw new Error(`WebAV文件验证失败: ${validationResult.error}`);
        }

        let fileObject = validationResult.fileObject;
        let actualPath = validationResult.actualPath;

        if (validationResult.fallbackUsed) {
          console.log(`🎬 WebAV: 使用降级方案，原始文件路径: ${actualPath}`);
        } else {
          console.log(`✅ WebAV: 直接使用请求文件: ${actualPath}`);
        }

        // 验证文件对象
        if (!fileObject) {
          throw new Error(`文件对象为空: ${actualPath}`);
        }

        console.log(`📊 OTFile对象详情:`, {
          name: clip.name,
          requestedPath: clip.path,
          actualPath: actualPath,
          isOTFile: true,
          hasFileMethod: 'file' in fileObject,
          hasSize: 'size' in fileObject,
          hasStream: 'stream' in fileObject,
          hasExists: 'exists' in fileObject,
          objectKeys: Object.keys(fileObject),
          objectMethods: Object.getOwnPropertyNames(Object.getPrototypeOf(fileObject))
        });

        // 简化处理：直接验证OTFile对象并使用
        console.log(`📄 直接使用OTFile对象: ${clip.name}`);
        console.log(`� OTFile对象类型: ${fileObject.constructor?.name || 'unknown'}`);

        // 基本验证：确保对象存在且有stream方法
        if (!fileObject) {
          throw new Error(`OTFile对象为空: ${actualPath}`);
        }

        if (typeof fileObject.stream !== 'function') {
          throw new Error(`OTFile对象不支持stream方法: ${actualPath}`);
        }

        // 简单的文件头验证（可选）
        try {
          const stream = await fileObject.stream();
          const reader = stream.getReader();
          const { value, done } = await reader.read();
          reader.releaseLock();

          if (!done && value && value.length >= 8) {
            console.log(`� 文件头验证: ${clip.name}, 头部长度: ${value.length} bytes`);

            // 检查MP4文件头标识
            const header = new Uint8Array(value.slice(0, 8));
            const hasValidHeader = (
              // ftyp box
              (header[4] === 0x66 && header[5] === 0x74 && header[6] === 0x79 && header[7] === 0x70) ||
              // moov box
              (header[4] === 0x6D && header[5] === 0x6F && header[6] === 0x6F && header[7] === 0x76)
            );

            if (hasValidHeader) {
              console.log(`✅ 文件头验证通过: ${clip.name}`);
            } else {
              console.warn(`⚠️ 文件头验证失败，但继续处理: ${clip.name}`);
            }
          }
        } catch (error) {
          console.warn(`⚠️ 文件头验证异常，但继续处理: ${clip.name}`, error);
        }

        console.log(`✅ OTFile验证完成: ${clip.name}, 路径: ${actualPath}`);

        // 原版方案：获取原始文件路径用于共享
        let originalFilePath = actualPath;

        // 如果是分段片段，获取原始文件路径
        if (clip.id.includes('_seg_')) {
          console.log(`🔗 检测到分段片段: ${clip.name}`);

          if (clip.originalPath) {
            // 优先使用originalPath字段
            originalFilePath = clip.originalPath;
            console.log(`✅ 使用originalPath: ${originalFilePath}`);
          } else {
            // 从分段路径中提取原始路径
            originalFilePath = actualPath.replace(/_seg_\d+/, '');
            console.log(`✅ 从路径提取原始文件: ${originalFilePath}`);
          }

          // 验证原始文件是否存在
          try {
            const originalFileObject = await getFile(originalFilePath);
            if (originalFileObject) {
              console.log(`✅ 原始文件验证成功: ${originalFilePath}`);
              // 使用原始文件对象
              fileObject = originalFileObject;
              actualPath = originalFilePath;
            }
          } catch (error) {
            console.warn(`⚠️ 原始文件不存在，使用分段文件: ${originalFilePath}`, error);
            originalFilePath = actualPath; // 回退到分段文件
          }
        }

        console.log(`🎬 原版方案：获取共享MP4Clip: ${clip.name} (文件: ${originalFilePath})`);

        let sharedMP4Clip;
        if (sharedVideoClips.has(originalFilePath)) {
          // 复用已存在的MP4Clip
          sharedMP4Clip = sharedVideoClips.get(originalFilePath);
          console.log(`📎 复用共享MP4Clip: ${originalFilePath}`);
        } else {
          // 创建新的MP4Clip并缓存
          console.log(`🎬 创建新的共享MP4Clip: ${originalFilePath}`);
          sharedMP4Clip = await Promise.race([
            createMP4ClipWithValidation(fileObject, clip.name, originalFilePath),
            createTimeoutPromise(45000, 'MP4Clip创建')
          ]);
          sharedVideoClips.set(originalFilePath, sharedMP4Clip);
          console.log(`✅ 共享MP4Clip创建并缓存: ${originalFilePath}`);
        }

        // 原版方案：直接使用共享MP4Clip，不进行split
        const mp4Clip = sharedMP4Clip;

        console.log(`🎯 原版分段方案 - ${clip.name}:`, {
          sourceStartTime: clip.sourceStartTime,
          sourceEndTime: clip.sourceEndTime,
          originalDuration: clip.originalDuration,
          timelineStart: clip.startTime,
          timelineDuration: clip.duration
        });

        // 设置tickInterceptor处理时间范围和音量
        mp4Clip.tickInterceptor = async (time, tickRet) => {
          // 原版方案：在tickInterceptor中处理sourceStartTime偏移
          if (clip.sourceStartTime && Number(clip.sourceStartTime) > 0) {
            // 调整时间：加上源开始时间偏移
            const adjustedTime = time + Number(clip.sourceStartTime) * 1e6;

            // 检查是否超出源结束时间
            if (clip.sourceEndTime && adjustedTime > Number(clip.sourceEndTime) * 1e6) {
              return {
                audio: [],
                video: null,
                state: 'success' as const
              };
            }

            // 使用调整后的时间获取帧数据
            // 注意：这里可能需要重新调用原始MP4Clip的tick方法
            console.log(`⏰ 时间调整: 原始${time/1e6}s -> 调整后${adjustedTime/1e6}s`);
          }

          // 音量处理
          let list = [];
          for (const audio of tickRet.audio) {
            list.push(
              audio.map((value) => (value = value * (clip.volume / 100)))
            );
          }
          tickRet.audio = list;

          // 处理视频画面
          tickRet.video = await applyVideoEffect(tickRet.video, clip, time / 1e6);
          return tickRet;
        };

        // 如果是分段片段，记录时间范围信息（用于调试）
        if (clip.id.includes('_seg_')) {
          console.log(`🎬 分段片段时间范围: ${clip.sourceStartTime}s - ${clip.sourceEndTime}s (共享文件: ${actualPath})`);
          console.log(`📊 分段信息:`, {
            clipId: clip.id,
            sourceStartTime: clip.sourceStartTime,
            sourceEndTime: clip.sourceEndTime,
            duration: clip.duration,
            startTime: clip.startTime
          });
        }

        console.log(`✅ 共享MP4Clip配置完成: ${clip.name} (共享文件: ${actualPath})`);

        // 不再使用split，而是通过Sprite的时间控制来实现分段
        // 这是内存优化的关键：所有分段共享同一个解码器
      try {
        spr = new VisibleSprite(mp4Clip);
        console.log(`✅ VisibleSprite创建成功: ${clip.name}`);
      } catch (spriteError) {
        console.error(`❌ VisibleSprite创建失败: ${clip.name}`, spriteError);
        throw new Error(`VisibleSprite创建失败: ${clip.name} - ${spriteError.message}`);
      }

      // 检测并修复视频旋转问题
      if (clip.name && clip.name.toLowerCase().includes('publishvideo')) {
        if (!clip.angle || clip.angle === 0) {
          clip.angle = -Math.PI / 2; // -90度纠正
          console.log(`🔧 修复 ${clip.name} 的旋转角度为 -90度`);
        }
      }

      // 调试信息：视频片段初始化
      console.log(`🎬 视频片段初始化 - ${clip.name}:`, {
        clipAngle: clip.angle,
        clipAngleDegrees: clip.angle ? clip.angle * (180 / Math.PI) : 0,
        clipType: clip.type,
        clipPath: clip.path,
        mp4ClipReady: !!mp4Clip,
        spriteCreated: !!spr
      });

      break;
    }

    case 'audio': {
      const fileObject = await getFile(clip.path);
      let audioClip = await new AudioClip(await fileObject.stream());
      if (Number(clip.sourceStartTime) > 0) {
        const newStartClips = await audioClip.split(
          Number(clip.sourceStartTime) * 1e6
        );
        if (Number(clip.sourceEndTime)) {
          const newEndClips = await newStartClips[1].split(
            (Number(clip.originalDuration) -
              Number(clip.sourceEndTime) -
              Number(clip.sourceStartTime)) *
              1e6
          );
          audioClip = newEndClips[0];
        } else {
          audioClip = newStartClips[1];
        }
      }
      audioClip.tickInterceptor = async (time, tickRet) => {
        tickRet.audio = tickRet.audio.map(
          (value) => new Float32Array(value.map((v) => v * (clip.volume / 100)))
        );
        return tickRet;
      };
      spr = new VisibleSprite(audioClip);
      break;
    }

    case 'image': {
      const fileObject = await getFile(clip.path);
      let imgClip = null;
      if (clip.isAnimateImg) {
        const mineType = fileObject.name.split('.').pop()?.toLowerCase();
        imgClip = new ImgClip({
          type: `image/${mineType}` as any,
          stream: await fileObject.stream(),
        });
      } else {
        imgClip = new ImgClip(await fileObject.stream());
      }
      spr = new VisibleSprite(imgClip);
      break;
    }

    case 'text': {
      const textClip = new TextClip(clip.textConfig, (width, height) => {
        if (!isPlaying.value) {
          emit('updateClipProps', {
            id: clip.id,
            w: width,
            h: height,
          });
        }
      });
      spr = new VisibleSprite(textClip);
      break;
    }

    case 'filter': {
      const filterClip = new FilterClip(Number(clip.duration) * 1e6);
      spr = new VisibleSprite(filterClip);
      spr.visible = false;
      break;
    }
  }
  // 设置更新订阅
  trackStore.unsubscribeFromClipUpdates(clip.id);
  trackStore.subscribeToClipUpdates(clip.id!, async (updatedClip, type) => {
    updateClip(updatedClip, type);
  });

    if (!spr) {
      throw new Error(`无法创建sprite: ${clip.name}`);
    }

    // 原版方案：通过时间范围控制分段播放
    const offsetMicros = Number(clip.startTime) * 1e6;
    const durationMicros = Number(clip.duration) * 1e6;

    // 设置Sprite在时间轴上的位置和时长
    spr.time.offset = offsetMicros;
    spr.time.duration = durationMicros;

    // 关键：如果有sourceStartTime，需要调整MP4Clip的播放起点
    if (clip.sourceStartTime && Number(clip.sourceStartTime) > 0) {
      // 通过修改MP4Clip的时间偏移来实现分段播放
      // 这里我们需要让MP4Clip从sourceStartTime开始播放
      console.log(`🎯 分段时间控制 - ${clip.name}:`, {
        timelinePosition: clip.startTime,
        timelineDuration: clip.duration,
        sourceStartTime: clip.sourceStartTime,
        sourceEndTime: clip.sourceEndTime
      });
    }

    spr.opacity = Number((clip.opacity / 100).toFixed(2));
    spr.rect.fixedScaleCenter = true;
    spr.rect.fixedAspectRatio = true;

    // 调试信息：显示Sprite时间配置
    console.log(`⏰ 原版Sprite时间配置 - ${clip.name}:`, {
      startTime: clip.startTime,
      duration: clip.duration,
      sourceStartTime: clip.sourceStartTime,
      sourceEndTime: clip.sourceEndTime,
      offsetMicros: offsetMicros,
      durationMicros: durationMicros,
      opacity: spr.opacity,
      isSharedClip: true
    });

    // 特殊处理：在sprite添加前就修复特定视频的旋转问题
    if (clip.name && clip.name.toLowerCase().includes('publishvideo') && clip.type === 'video') {
      spr.rect.angle = -Math.PI / 2; // 强制设置为-90度
      console.log(`🔧 Sprite创建时立即修复 ${clip.name} 的旋转角度为 -90度`);
    }

    console.log(`🎨 添加Sprite到Canvas: ${clip.name}`);

    // 验证 canvas 和 sprite 状态
    if (!cvs) {
      throw new Error(`Canvas未初始化: ${clip.name}`);
    }

    if (!spr) {
      throw new Error(`Sprite创建失败: ${clip.name}`);
    }

    // 添加到 sprite map 和 canvas
    sprMap.set(clip.id, spr);
    await cvs?.addSprite(spr);
    initCount.value++;

    // 设置位置和大小 - 关键的原版逻辑！
    if (clip.type !== 'audio') {
      if (!clip.w) {
        // 没有宽度时，发送事件设置默认值
        emit('updateClipProps', {
          id: clip.id,
          x: spr.rect.x,
          y: spr.rect.y,
          w: spr.rect.w,
          h: spr.rect.h,
          angle: spr.rect.angle,
        });
      } else {
        // 有宽度时，直接设置
        spr.rect.x = clip.x;
        spr.rect.y = clip.y;
        spr.rect.w = clip.w;
        spr.rect.h = clip.h;
        spr.rect.angle = clip.angle;
      }
    }

    // 设置事件监听
    spr.rect.on('propsChange', (event) => {
      if (clip.type === 'text') {
        if ('w' in event && clip.w) {
          clip.textConfig.fontSize =
            (event.w / clip.w) * clip.textConfig.fontSize;
          clip.w = event.w;
        }
      } else {
        const props = {
          id: clip.id,
          ...event,
        };
        emit('updateClipProps', props);
      }
    });

    // 清理初始化标记
    initializingClips.delete(clip.id);

  } catch (error) {
    console.error(`❌ 片段初始化失败: ${clip.name} (尝试 ${retryCount + 1}/${maxRetries + 1})`, error);

    // 清理初始化标记
    initializingClips.delete(clip.id);

    // 特殊处理存储配额错误
    const isQuotaError = error.message.includes('QuotaExceededError') ||
                        error.message.includes('storage quota') ||
                        error.message.includes('exceed its storage');

    if (isQuotaError) {
      console.error('💾 存储配额超限，尝试清理并重试...');

      // 清理临时文件
      await cleanupTempFiles();

      // 如果是配额错误且还有重试机会，延长重试间隔
      if (retryCount < maxRetries) {
        console.log(`🔄 存储清理后重试: ${clip.name} (${retryCount + 1}/${maxRetries})`);

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, 3000 * (retryCount + 1)));
        return await initClip(clip, retryCount + 1);
      } else {
        // 配额错误且重试次数用完
        initCount.value++;
        ElMessage.error(`存储空间不足，无法加载 ${clip.name}。请清理浏览器数据后重试。`);
        return;
      }
    }

    // 检查是否需要重试（非配额错误）
    if (retryCount < maxRetries && error.message.includes('MP4Clip')) {
      console.log(`🔄 准备重试片段初始化: ${clip.name} (${retryCount + 1}/${maxRetries})`);

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
      return await initClip(clip, retryCount + 1);
    }

    // 最终失败，增加计数以避免永久加载
    initCount.value++;

    // 显示错误提示
    let errorMsg;
    if (isQuotaError) {
      errorMsg = `存储空间不足，无法加载 ${clip.name}`;
    } else if (retryCount > 0) {
      errorMsg = `视频片段 ${clip.name} 重试 ${retryCount} 次后仍然失败: ${error.message}`;
    } else {
      errorMsg = `视频片段 ${clip.name} 加载失败: ${error.message}`;
    }
    ElMessage.error(errorMsg);

    // 清理可能的部分状态
    if (sprMap.has(clip.id)) {
      sprMap.delete(clip.id);
    }
  }
};

// PIXI应用实例，用于滤镜处理
let app: PIXI.Application | null = null;

const initPixiApp = () => {
  if (app) {
    console.log('🧹 销毁现有PIXI应用');
    app.destroy(true);
    app = null;
  }

  console.log('🎨 创建新的PIXI应用');
  app = new PIXI.Application({
    width: canvasSize.value.width,
    height: canvasSize.value.height,
    backgroundAlpha: 0,
  });

  console.log('✅ PIXI应用创建完成:', app);
};

// 旋转视频帧
const rotateVideoFrame = async (frame: any, degrees: number): Promise<any> => {
  if (!frame) return frame;

  try {
    // 获取视频帧的尺寸信息
    const width = frame.displayWidth || frame.codedWidth || frame.width || 0;
    const height = frame.displayHeight || frame.codedHeight || frame.height || 0;

    console.log(`🔍 视频帧尺寸: ${width}x${height}`);

    // 检查尺寸是否有效
    if (width <= 0 || height <= 0) {
      console.warn('⚠️ 视频帧尺寸无效，跳过旋转');
      return frame;
    }

    // 创建canvas来处理旋转
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return frame;

    // 根据旋转角度调整canvas尺寸
    if (Math.abs(degrees) === 90 || Math.abs(degrees) === 270) {
      canvas.width = height;
      canvas.height = width;
    } else {
      canvas.width = width;
      canvas.height = height;
    }

    console.log(`🎨 Canvas尺寸: ${canvas.width}x${canvas.height}, 旋转角度: ${degrees}度`);

    // 设置旋转变换
    ctx.save();
    ctx.translate(canvas.width / 2, canvas.height / 2);
    ctx.rotate((degrees * Math.PI) / 180);
    ctx.translate(-width / 2, -height / 2);

    // 尝试不同的绘制方法
    try {
      // 方法1：直接绘制VideoFrame
      ctx.drawImage(frame, 0, 0, width, height);
      console.log('✅ 使用VideoFrame直接绘制成功');
    } catch (error) {
      console.warn('⚠️ VideoFrame直接绘制失败，尝试其他方法:', error);

      try {
        // 方法2：创建临时canvas
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');
        tempCanvas.width = width;
        tempCanvas.height = height;

        if (tempCtx) {
          tempCtx.drawImage(frame, 0, 0);
          ctx.drawImage(tempCanvas, 0, 0, width, height);
          console.log('✅ 使用临时canvas绘制成功');
        }
      } catch (error2) {
        console.error('❌ 所有绘制方法都失败了:', error2);
      }
    }

    ctx.restore();

    // 创建新的VideoFrame
    const newFrame = new VideoFrame(canvas, {
      timestamp: frame.timestamp,
      duration: frame.duration,
    });

    // 关闭原始帧
    frame.close();

    console.log('✅ 视频帧旋转成功');
    return newFrame;
  } catch (error) {
    console.error('旋转视频帧失败:', error);
    return frame;
  }
};

// 应用视频效果
const applyVideoEffect = async (
  frame: VideoFrame,
  targetClip: TrackClip,
  time: number
): Promise<VideoFrame> => {
  if (!frame) return null;
  const displayWidth = frame.displayWidth;
  const displayHeight = frame.displayHeight;

  // 查找当前时间点生效的滤镜
  const activeFilters = props.tracks
    .flatMap((track) => track.clips)
    .filter(
      (clip) =>
        clip.type === 'filter' &&
        time + targetClip.startTime >= clip.startTime &&
        time + targetClip.startTime < clip.startTime + clip.duration
    ) as FilterTrackClip[];
  if (!activeFilters.length) return frame;

  // 确保PIXI应用已初始化
  if (!app) {
    console.warn('⚠️ PIXI应用未初始化，跳过滤镜处理');
    return frame;
  }

  app.stage.removeChildren();
  // 创建视频纹理
  const videoTexture = PIXI.Texture.from(frame);
  const sprite = PIXI.Sprite.from(videoTexture);
  app.stage.addChild(sprite);

  // 按顺序应用滤镜
  const filters: PIXI.Filter[] = [];
  for (const filter of activeFilters) {
    const intensity = filter.intensity / 100;

    switch (filter.filterType) {
      case 'grayscale': {
        const colorMatrix = new PIXI.ColorMatrixFilter();
        colorMatrix.grayscale(intensity, false);
        filters.push(colorMatrix);
        break;
      }
      case 'sepia': {
        const colorMatrix = new PIXI.ColorMatrixFilter();
        colorMatrix.sepia(false);
        filters.push(colorMatrix);
        break;
      }
      case 'invert': {
        const colorMatrix = new PIXI.ColorMatrixFilter();
        colorMatrix.negative(false);
        filters.push(colorMatrix);
        break;
      }
      case 'brightness': {
        const brightnessFilter = new PIXI.ColorMatrixFilter();
        brightnessFilter.brightness(1 + intensity, false);
        filters.push(brightnessFilter);
        break;
      }
      case 'blur': {
        const blurFilter = new PIXI.BlurFilter();
        blurFilter.blur = intensity * 10;
        filters.push(blurFilter);
        break;
      }
    }
  }
  sprite.filters = filters;
  sprite.filterArea = new PIXI.Rectangle(0, 0, displayWidth, displayHeight);
  // 清理资源
  const timestamp = frame.timestamp;
  const duration = frame.duration;

  // 创建新的视频帧
  const canvas = app.renderer.extract.canvas(app.stage);
  videoTexture.destroy(true);
  frame.close();
  return new VideoFrame(canvas as HTMLCanvasElement, {
    timestamp: timestamp,
    duration: duration,
    displayWidth: displayWidth,
    displayHeight: displayHeight,
  });
};

// 更新相关方法
const updateSpritesZIndex = () => {
  let currentZIndex = 0;
  props.tracks.forEach((track) => {
    track.clips.forEach((clip) => {
      const spr = sprMap.get(clip.id);
      if (!spr || clip.type === 'text') return;
      spr.zIndex = currentZIndex++;
    });
    track.clips.forEach((clip) => {
      const spr = sprMap.get(clip.id);
      if (!spr || clip.type !== 'text') return;
      spr.zIndex = currentZIndex++;
    });
  });
};

const updateClip = async (
  clip: TrackClip,
  type: 'default' | 'resize' | 'delete' = 'default'
) => {
  handlePlay(false);
  if (!sprMap.has(clip.id)) return;

  const spr = sprMap.get(clip.id);

  if (type === 'delete') {
    if (clip.type === 'filter') {
      props.tracks.forEach((track) => {
        track.clips.forEach((clipItem) => {
          if (
            props.currentTime >= clipItem.startTime &&
            props.currentTime < clipItem.startTime + clipItem.duration
          ) {
            const sprItem = sprMap.get(clipItem.id);
            sprItem.preFrame(props.currentTime * 1e6 - sprItem.time.offset);
          }
        });
      });
    }
    cvs?.removeSprite(spr);
    sprMap.delete(clip.id);

    // 删除后更新Canvas预览帧
    console.log(`🗑️ 片段删除后状态:`, {
      clipName: clip.name,
      remainingSprites: sprMap.size,
      currentTime: props.currentTime,
      canvasSize: canvasSize.value
    });

    cvs?.previewFrame(props.currentTime * 1e6);

    // 如果没有剩余的Sprite，清空Canvas显示
    if (sprMap.size === 0) {
      console.log(`🧹 所有片段已删除，清空Canvas显示`);
      // 可以选择显示黑屏或者其他默认状态
    }

    return;
  }

  if (type === 'resize' && (clip.type === 'video' || clip.type === 'audio')) {
    cvs?.removeSprite(spr);
    sprMap.delete(clip.id);
    await initClip(clip);
    return;
  }

  spr.time.offset = clip.startTime * 1e6;
  spr.time.duration = Number(clip.duration) * 1e6;
  spr.opacity = Number((clip.opacity / 100).toFixed(2));

  updateSpritesZIndex();
  if (clip.type === 'text') {
    const textClip: TextClip = spr.getClip() as TextClip;
    textClip.textConfig = clip.textConfig;
    spr.rect.w = clip.w;
    spr.rect.h = clip.h;
    spr.preFrame(props.currentTime * 1e6);
  } else if (clip.type === 'filter') {
    props.tracks.forEach((track) => {
      track.clips.forEach((clipItem) => {
        if (
          (clipItem.type === 'video' || clipItem.type === 'image') &&
          clipItem.startTime <= props.currentTime &&
          clipItem.endTime >= props.currentTime
        ) {
          const sprItem = sprMap.get(clipItem.id);
          if (sprItem) {
            sprItem.preFrame(props.currentTime * 1e6 - sprItem.time.offset);
          }
        }
      });
    });
  } else if (clip.type !== 'audio') {
    // 调试信息：显示Sprite位置和大小
    console.log(`🎨 设置Sprite位置和大小 - ${clip.name}:`, {
      x: clip.x,
      y: clip.y,
      w: clip.w,
      h: clip.h,
      canvasSize: { width: 1920, height: 1080 }
    });

    // 检查位置和大小是否有效
    const x = clip.x || 0;
    const y = clip.y || 0;
    const w = clip.w || 1920;
    const h = clip.h || 1080;

    // 确保位置和大小在合理范围内
    if (w <= 0 || h <= 0) {
      console.warn(`⚠️ Sprite尺寸无效: ${clip.name}, w=${w}, h=${h}, 使用默认值`);
      spr.rect.w = 1920;
      spr.rect.h = 1080;
    } else {
      spr.rect.w = w;
      spr.rect.h = h;
    }

    spr.rect.x = x;
    spr.rect.y = y;

    // 特殊处理：修复特定视频的旋转问题
    let finalAngle = clip.angle || 0;
    if (clip.name && clip.name.toLowerCase().includes('publishvideo')) {
      finalAngle = -Math.PI / 2; // 强制设置为-90度
      console.log(`🔧 强制修复 ${clip.name} 的旋转角度为 -90度`);
    }

    spr.rect.angle = finalAngle;

    // 调试信息：显示最终的Sprite配置
    console.log(`🎬 Sprite最终配置 - ${clip.name}:`, {
      position: { x: spr.rect.x, y: spr.rect.y },
      size: { w: spr.rect.w, h: spr.rect.h },
      angle: spr.rect.angle,
      angleDegrees: spr.rect.angle * (180 / Math.PI),
      visible: true
    });
  }

  // 更新Canvas预览帧
  const currentTimeMicros = props.currentTime * 1e6;
  console.log(`🎬 更新Canvas预览帧: ${props.currentTime}s (${currentTimeMicros}μs)`);

  try {
    cvs?.previewFrame(currentTimeMicros);
    console.log(`✅ Canvas预览帧更新成功`);
  } catch (previewError) {
    console.error(`❌ Canvas预览帧更新失败:`, previewError);
  }
};

const refreshPlayer = () => {
  cvs?.destroy();
  sprMap.clear();
  initializingClips.clear();

  // 清理共享视频资源
  console.log('🧹 清理共享视频资源...');
  for (const [path, clip] of sharedVideoClips) {
    clip.destroy();
    console.log(`🗑️ 销毁共享MP4Clip: ${path}`);
  }
  sharedVideoClips.clear();

  // 检查是否有视频内容
  updateVideoContentStatus();

  initCount.value = 0;
  nextTick(() => {
    initCanvas();
  });
};

const exporting = ref(false);
const outputRenderTime = ref(0);
// 导出相关
const handleExport = async () => {
  const com = await cvs?.createCombinator();

  try {
    const options: ElMessageBoxOptions = {
      dangerouslyUseHTMLString: true,
      showClose: false,
      closeOnClickModal: false,
      closeOnPressEscape: false,
      showCancelButton: true,
      showConfirmButton: false,
      cancelButtonText: '停止',
      customClass: 'export-progress-dialog',
      beforeClose: (action, instance, done) => {
        if (action === 'cancel') {
          com?.destroy();
          exporting.value = false;
          outputRenderTime.value = 0;
          done();
        }
      },
    };

    ElMessageBox.alert(
      `
            <div class="flex flex-col gap-2 w-full min-w-[300px]">
                <div class="text-center text-base">0%</div>
                <div class="w-full bg-[#2b2b2b] rounded-full h-2">
                    <div class="bg-purple-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
            </div>
        `,
      '导出进度',
      options
    );

    await nextTick();

    com.on('renderTime', (time: number) => {
      outputRenderTime.value = time;
    });

    com.on('OutputProgress', (prog: number) => {
      const percentage = Math.round(prog * 100);
      const messageEl = document.querySelector('.el-message-box__message');
      if (messageEl) {
        messageEl.innerHTML = `
                    <div class="flex flex-col gap-2 w-full min-w-[300px]">
                        <div class="text-center text-base">${percentage}%</div>
                        <div class="w-full bg-[#2b2b2b] rounded-full h-2">
                            <div class="bg-purple-600 h-2 rounded-full transition-all duration-300" style="width: ${percentage}%"></div>
                        </div>
                    </div>
                `;

        if (prog === 1) {
          exporting.value = false;
          outputRenderTime.value = 0;
          const cancelBtn = document.querySelector(
            '.el-message-box__cancel'
          ) as HTMLElement;
          if (cancelBtn) {
            cancelBtn.style.display = 'none';
          }
          messageEl.innerHTML = `
                        <div class="flex flex-col gap-2 w-full min-w-[300px]">
                            <div class="text-center text-green-500 text-base">导出完成！</div>
                            <div class="w-full bg-[#2b2b2b] rounded-full h-2">
                                <div class="bg-purple-600 h-2 rounded-full transition-all duration-300" style="width: 100%"></div>
                            </div>
                        </div>
                    `;
          setTimeout(async () => {
            ElMessageBox.close();

            // 🎯 导出完成后自动跳转到结果页面
            console.log('🚀 导出完成，准备跳转到结果页面...');
            try {
              const router = useRouter();
              await router.push('/result');
              console.log('✅ 已跳转到结果页面');
            } catch (error) {
              console.error('❌ 跳转到结果页面失败:', error);
            }
          }, 3000);
        }
      }
    });
    exporting.value = true;

    // 创建文件写入器并确保正确关闭
    let fileWriter: FileSystemWritableFileStream | null = null;
    try {
      fileWriter = await createFileWriter();
      await com?.output().pipeTo(fileWriter);
    } finally {
      // 确保文件写入器被正确关闭
      if (fileWriter) {
        try {
          await fileWriter.close();
        } catch (closeError) {
          console.warn('⚠️ 文件写入器关闭时出现警告:', closeError);
        }
      }
    }
  } catch (error: any) {
    ElMessageBox.close();
    ElMessage.error('导出失败：' + error.message);
    console.error('❌ 导出过程中发生错误:', error);
  }
};

// 工具方法
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const handleResize = () => {
  if (playerRef.value) {
    const observer = new ResizeObserver((entries) => {
      for (let entry of entries) {
        const { width, height } = entry.contentRect;
        const toolBarHeight =
          document.querySelector('.control-bar')?.clientHeight;
        const playerHeight = height - toolBarHeight;
        if (width / playerHeight > 16 / 9) {
          trackStore.setCanvasSize({
            width: (playerHeight * 16) / 9,
            height: playerHeight,
          });
        } else {
          trackStore.setCanvasSize({ width: width, height: (width * 9) / 16 });
        }
      }
    });
    observer.observe(playerRef.value);
  }
};

// 生命周期钩子
onMounted(() => {
  Log.setLogLevel(Log.warn);
  initCanvas();
  handleResize();
  window.addEventListener('keydown', handleKeyPress);
});

onUnmounted(() => {
  console.log('🧹 播放器组件卸载，清理资源...');

  // 清理AVCanvas
  if (cvs) {
    cvs.destroy();
    cvs = null;
  }

  // 清理PIXI应用
  if (app) {
    app.destroy(true);
    app = null;
  }

  // 清理sprite映射和订阅
  for (const clipId of sprMap.keys()) {
    trackStore.unsubscribeFromClipUpdates(clipId);
  }
  sprMap.clear();

  // 清理共享视频资源
  for (const [path, clip] of sharedVideoClips) {
    clip.destroy();
  }
  sharedVideoClips.clear();

  initCount.value = 0;

  // 清理加载超时检测
  if (loadingTimeout.value) {
    clearTimeout(loadingTimeout.value);
    loadingTimeout.value = null;
  }

  window.removeEventListener('keydown', handleKeyPress);
  document.removeEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement;
  });

  console.log('✅ 播放器资源清理完成');
});

// 监听器
watch(
  () => initCount.value,
  (newCount) => {
    console.log(`🔄 加载进度: ${newCount}/${initTotal.value}`);

    if (initTotal.value !== 0) {
      loading.value = true;

      // 设置加载超时检测
      if (loadingTimeout.value) {
        clearTimeout(loadingTimeout.value);
      }

      loadingTimeout.value = setTimeout(() => {
        console.warn('⚠️ 加载超时，强制结束加载状态');
        loading.value = false;
        ElMessage.warning('部分视频片段加载超时，请检查文件完整性');
        loadingTimeout.value = null;
      }, 60000); // 60秒超时
    }

    if (newCount >= initTotal.value) {
      loading.value = false;

      // 清除超时检测
      if (loadingTimeout.value) {
        clearTimeout(loadingTimeout.value);
        loadingTimeout.value = null;
      }

      console.log(`✅ 所有片段加载完成: ${newCount}/${initTotal.value}`);

      cvs?.previewFrame(props.currentTime * 1e6);
      for (const clip of props.tracks.flatMap((track) => track.clips)) {
        updateSpritesZIndex();
      }
      // 需要等待cvs渲染完成
      setTimeout(() => {
        try {
          const dataUrl = cvs?.captureImage();
          if (dataUrl && typeof dataUrl === 'string' && dataUrl.startsWith('data:image/')) {
            emit('captureImage', dataUrl);
          } else {
            console.warn('⚠️ Player: captureImage返回无效数据:', dataUrl);
          }
        } catch (captureError) {
          console.error('❌ Player: captureImage执行失败:', captureError);
        }
      }, 500);
    }
  },
  { immediate: true }
);

watch(
  () => props.currentTime,
  (newTime) => {
    if (!isPlaying.value) {
      cvs.previewFrame(newTime * 1e6);
    }
  }
);

// 🔧 监听轨道变化，处理分割后的canvas尺寸问题
watch(
  () => props.tracks,
  (newTracks, oldTracks) => {
    console.log('🔄 轨道数据变化，检查canvas状态');

    // 检查是否是分割操作（轨道数量相同但片段数量增加）
    const oldClipCount = oldTracks?.reduce((count, track) => count + track.clips.length, 0) || 0;
    const newClipCount = newTracks?.reduce((count, track) => count + track.clips.length, 0) || 0;

    if (newClipCount > oldClipCount) {
      console.log('🔧 检测到分割操作，重新计算canvas尺寸');

      nextTick(() => {
        // 重新计算canvas尺寸
        if (playerRef.value) {
          const { width, height } = playerRef.value.getBoundingClientRect();
          const toolBarHeight = document.querySelector('.control-bar')?.clientHeight || 0;
          const playerHeight = height - toolBarHeight;

          let newCanvasSize;
          if (width / playerHeight > 16 / 9) {
            newCanvasSize = {
              width: (playerHeight * 16) / 9,
              height: playerHeight,
            };
          } else {
            newCanvasSize = {
              width: width,
              height: (width * 9) / 16
            };
          }

          console.log('🎨 重新设置canvas尺寸:', {
            oldSize: canvasSize.value,
            newSize: newCanvasSize,
            playerSize: { width, height },
            playerHeight
          });

          trackStore.setCanvasSize(newCanvasSize);
        }
      });
    }
  },
  { deep: true }
);

// 导出方法
defineExpose({
  handleStopPlay,
  refreshPlayer,
  addClip,
  activeClip,
  handleExport,
});

// 添加样式
const style = document.createElement('style');
style.textContent = `
.export-progress-dialog .el-message-box__content {
    padding: 20px;
}
.export-progress-dialog .el-message-box__message {
    padding: 0;
}
.export-progress-dialog .el-message-box__message p {
    margin: 0;
}
.export-progress-dialog .el-message-box__container {
    width: 100%;
}
`;
document.head.appendChild(style);
</script>

<style lang="scss" scoped>
.player-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.control-overlay {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, transparent 100%);
}

/* 自定义滑块样式 */
input[type='range'] {
  appearance: none;
  -webkit-appearance: none;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
}

input[type='range']::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  background: theme('colors.purple.500');
  border-radius: 50%;
  cursor: pointer;
}

input[type='range']::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: theme('colors.purple.500');
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.export-progress-dialog {
  .el-message-box__container {
    width: 100%;

    .el-message-box__message {
      width: 100%;
    }
  }
}
</style>
