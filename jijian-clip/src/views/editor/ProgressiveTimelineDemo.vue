<template>
  <div class="progressive-timeline-demo">
    <!-- 头部信息 -->
    <div class="demo-header">
      <h1>🚀 渐进式时间轴升级演示</h1>
      <p>在视频剪辑页面中体验从经典时间轴到新版片段视图的平滑过渡</p>
    </div>

    <!-- 控制面板 -->
    <div class="demo-controls">
      <div class="control-group">
        <h3>数据控制</h3>
        <button @click="loadSampleData">加载示例数据</button>
        <button @click="addRandomClip">添加随机片段</button>
        <button @click="clearData">清空数据</button>
      </div>

      <div class="control-group">
        <h3>升级控制</h3>
        <button @click="showUpgradeDemo">显示升级提示</button>
        <button @click="resetPreferences">重置用户偏好</button>
        <button @click="toggleDefaultVersion">
          默认版本: {{ defaultVersion === 'new' ? '新版本' : '经典版' }}
        </button>
      </div>

      <div class="control-group">
        <h3>状态信息</h3>
        <div class="status-grid">
          <div class="status-item">
            <label>当前版本:</label>
            <span>{{ timelineVersion === 'new' ? '新版本' : '经典版' }}</span>
          </div>
          <div class="status-item">
            <label>轨道数量:</label>
            <span>{{ tracks.length }}</span>
          </div>
          <div class="status-item">
            <label>片段总数:</label>
            <span>{{ totalClips }}</span>
          </div>
          <div class="status-item">
            <label>当前时间:</label>
            <span>{{ formatTime(currentTime) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 模拟的视频剪辑界面 -->
    <div class="demo-editor">
      <!-- 顶部播放器区域（模拟） -->
      <div class="demo-player">
        <div class="player-mockup">
          <div class="player-screen">
            <div class="play-button" @click="togglePlay">
              {{ isPlaying ? '⏸️' : '▶️' }}
            </div>
            <div class="time-display">{{ formatTime(currentTime) }} / {{ formatTime(totalDuration) }}</div>
          </div>
          <div class="player-controls">
            <input 
              type="range" 
              :min="0" 
              :max="totalDuration" 
              v-model="currentTime"
              class="time-slider"
            />
          </div>
        </div>
      </div>

      <!-- 底部时间轴区域 -->
      <div class="demo-timeline">
        <!-- 版本控制栏 -->
        <TimelineVersionControl
          :currentVersion="timelineVersion"
          :defaultVersion="defaultVersion"
          @toggle="toggleTimelineVersion"
          @reset="resetTimelineVersion"
          @show-info="showUpgradeDemo"
        />
        
        <!-- 动态时间轴组件 -->
        <div class="timeline-container">
          <Transition name="timeline-switch" mode="out-in">
            <component 
              :is="currentTimelineComponent"
              :key="timelineKey"
              v-bind="timelineProps"
              @tracks-change="handleTracksChange"
              @clip-select="handleClipSelect"
              @clip-edit="handleClipEdit"
              @time-change="handleTimeChange"
            />
          </Transition>
        </div>
      </div>
    </div>

    <!-- 升级面板 -->
    <TimelineUpgradePanel
      :visible="showUpgradePanelVisible"
      @try-new="tryNewTimeline"
      @keep-classic="keepClassicTimeline"
      @close="hideUpgradePanel"
    />

    <!-- 事件日志 -->
    <div class="demo-log">
      <h3>操作日志</h3>
      <div class="log-content">
        <div 
          v-for="(log, index) in eventLogs" 
          :key="index"
          class="log-entry"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <button @click="clearLogs">清空日志</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import TimelineVersionControl from '@/views/clipPage/components/TimelineVersionControl.vue';
import TimelineUpgradePanel from '@/views/clipPage/components/TimelineUpgradeModal.vue';
import TimelineViewSwitcher from '@/components/timeline/TimelineViewSwitcher.vue';
import ClassicTimelineWrapper from '@/components/timeline/ClassicTimelineWrapper.vue';

// 模拟的Track和TrackClip类型
interface TrackClip {
  id: string;
  type: 'video' | 'audio' | 'text' | 'image';
  name: string;
  startTime: number;
  duration: number;
  endTime: number;
  volume?: number;
  thumbnail?: { url: string; timestamp: number }[];
  volumeData?: number[];
}

interface Track {
  id: string;
  clips: TrackClip[];
}

// 响应式数据
const tracks = ref<Track[]>([]);
const currentTime = ref(0);
const totalDuration = ref(120);
const isPlaying = ref(false);
const timelineVersion = ref<'new' | 'classic'>('classic');
const defaultVersion = ref<'new' | 'classic'>('classic');
const showUpgradePanelVisible = ref(false);
const timelineKey = ref(0);

// 事件日志
const eventLogs = ref<Array<{ time: string; message: string }>>([]);

// 计算属性
const totalClips = computed(() => {
  return tracks.value.reduce((sum, track) => sum + track.clips.length, 0);
});

const currentTimelineComponent = computed(() => {
  return timelineVersion.value === 'new' ? TimelineViewSwitcher : ClassicTimelineWrapper;
});

const timelineProps = computed(() => {
  return {
    tracks: tracks.value,
    currentTime: currentTime.value,
    totalDuration: totalDuration.value
  };
});

// 方法
const addLog = (message: string) => {
  const now = new Date();
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
  
  eventLogs.value.unshift({ time, message });
  
  if (eventLogs.value.length > 20) {
    eventLogs.value = eventLogs.value.slice(0, 20);
  }
};

const loadSampleData = () => {
  tracks.value = [
    {
      id: 'video-track-1',
      clips: [
        {
          id: 'clip-1',
          type: 'video',
          name: '开场视频',
          startTime: 0,
          duration: 15,
          endTime: 15,
          volume: 80,
          thumbnail: [{ url: '/api/placeholder/200/120', timestamp: 0 }]
        },
        {
          id: 'clip-2',
          type: 'video',
          name: '主要内容',
          startTime: 20,
          duration: 30,
          endTime: 50,
          volume: 90,
          thumbnail: [{ url: '/api/placeholder/200/120', timestamp: 0 }]
        }
      ]
    },
    {
      id: 'audio-track-1',
      clips: [
        {
          id: 'clip-3',
          type: 'audio',
          name: '背景音乐',
          startTime: 0,
          duration: 60,
          endTime: 60,
          volume: 50,
          volumeData: [0.1, 0.3, 0.5, 0.7, 0.4, 0.2]
        }
      ]
    }
  ];
  addLog('加载了示例数据');
};

const addRandomClip = () => {
  if (tracks.value.length === 0) {
    tracks.value.push({ id: 'auto-track-1', clips: [] });
  }

  const randomTrack = tracks.value[Math.floor(Math.random() * tracks.value.length)];
  const clipTypes = ['video', 'audio', 'text'] as const;
  const randomType = clipTypes[Math.floor(Math.random() * clipTypes.length)];
  
  const newClip: TrackClip = {
    id: `clip-${Date.now()}`,
    type: randomType,
    name: `随机${randomType}片段`,
    startTime: Math.floor(Math.random() * 60),
    duration: Math.floor(Math.random() * 20) + 5,
    endTime: 0,
    volume: Math.floor(Math.random() * 100)
  };
  
  newClip.endTime = newClip.startTime + newClip.duration;
  randomTrack.clips.push(newClip);
  
  addLog(`添加了随机${randomType}片段`);
};

const clearData = () => {
  tracks.value = [];
  currentTime.value = 0;
  addLog('清空了所有数据');
};

const togglePlay = () => {
  isPlaying.value = !isPlaying.value;
  addLog(isPlaying.value ? '开始播放' : '暂停播放');
};

const formatTime = (time: number): string => {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

// 渐进式升级相关方法
const toggleTimelineVersion = () => {
  const newVersion = timelineVersion.value === 'new' ? 'classic' : 'new';
  timelineVersion.value = newVersion;
  timelineKey.value++;
  addLog(`切换到${newVersion === 'new' ? '新版本' : '经典版'}时间轴`);
};

const resetTimelineVersion = () => {
  timelineVersion.value = defaultVersion.value;
  timelineKey.value++;
  addLog(`重置为${defaultVersion.value === 'new' ? '新版本' : '经典版'}时间轴`);
};

const showUpgradeDemo = () => {
  showUpgradePanelVisible.value = true;
  addLog('显示升级提示面板');
};

const hideUpgradePanel = () => {
  showUpgradePanelVisible.value = false;
  addLog('关闭升级提示面板');
};

const tryNewTimeline = () => {
  timelineVersion.value = 'new';
  timelineKey.value++;
  hideUpgradePanel();
  addLog('用户选择体验新版本时间轴');
};

const keepClassicTimeline = () => {
  timelineVersion.value = 'classic';
  timelineKey.value++;
  hideUpgradePanel();
  addLog('用户选择继续使用经典版时间轴');
};

const resetPreferences = () => {
  localStorage.removeItem('timeline-version-preference');
  localStorage.removeItem('timeline-upgrade-prompt-shown');
  addLog('重置了用户偏好设置');
};

const toggleDefaultVersion = () => {
  defaultVersion.value = defaultVersion.value === 'new' ? 'classic' : 'new';
  addLog(`切换默认版本为: ${defaultVersion.value}`);
};

// 事件处理
const handleTracksChange = (newTracks: Track[]) => {
  tracks.value = newTracks;
  addLog('轨道数据已更新');
};

const handleClipSelect = (clip: TrackClip) => {
  addLog(`选中片段: ${clip.name}`);
};

const handleClipEdit = (clip: TrackClip) => {
  addLog(`编辑片段: ${clip.name}`);
};

const handleTimeChange = (time: number) => {
  currentTime.value = time;
};

const clearLogs = () => {
  eventLogs.value = [];
};

// 生命周期
onMounted(() => {
  addLog('渐进式时间轴演示已加载');
  loadSampleData();
  
  // 延迟显示升级提示
  setTimeout(() => {
    if (!localStorage.getItem('timeline-upgrade-prompt-shown')) {
      showUpgradeDemo();
    }
  }, 3000);
});
</script>

<style scoped>
.progressive-timeline-demo {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.demo-header {
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  text-align: center;
}

.demo-header h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.demo-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.demo-controls {
  display: flex;
  gap: 20px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  flex-wrap: wrap;
}

.control-group {
  flex: 1;
  min-width: 200px;
}

.control-group h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
}

.control-group button {
  display: block;
  width: 100%;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: #0066cc;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 12px;
}

.control-group button:hover {
  background: #0052a3;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.status-item label {
  color: #666;
}

.status-item span {
  color: #333;
  font-weight: 500;
}

.demo-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
}

.demo-player {
  height: 200px;
  background: #2a2a2a;
  border-bottom: 1px solid #404040;
  display: flex;
  align-items: center;
  justify-content: center;
}

.player-mockup {
  width: 300px;
  background: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
}

.player-screen {
  height: 150px;
  background: linear-gradient(135deg, #333, #555);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.play-button {
  font-size: 32px;
  cursor: pointer;
  color: white;
  margin-bottom: 10px;
}

.time-display {
  color: white;
  font-size: 12px;
}

.player-controls {
  padding: 10px;
}

.time-slider {
  width: 100%;
  height: 4px;
  background: #404040;
  border-radius: 2px;
  outline: none;
}

.demo-timeline {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.timeline-container {
  flex: 1;
  overflow: hidden;
}

.timeline-switch-enter-active,
.timeline-switch-leave-active {
  transition: all 0.3s ease;
}

.timeline-switch-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.timeline-switch-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.demo-log {
  height: 150px;
  background: white;
  border-top: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.demo-log h3 {
  margin: 0;
  padding: 12px 16px;
  background: #f8f8f8;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
  color: #333;
}

.log-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.log-entry {
  display: flex;
  gap: 12px;
  padding: 2px 8px;
  font-size: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  font-family: monospace;
  color: #666;
  min-width: 60px;
}

.log-message {
  flex: 1;
  color: #333;
}

.demo-log button {
  padding: 8px 16px;
  background: #666;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 12px;
}

.demo-log button:hover {
  background: #555;
}
</style>
