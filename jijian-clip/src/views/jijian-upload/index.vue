<template>
  <div class="jijian-upload-page min-h-screen bg-[var(--bg-light)]">
    <!-- 顶部导航 -->
    <JijianHeader
      :is-authenticated="userStore.isLoggedIn"
      :user-name="userStore.userName"
      :user-avatar="userStore.userAvatar"
      @login-click="showLoginModal = true"
      @register-click="showRegisterModal = true"
      @upload-click="() => {}"
      @user-command="handleUserCommand"
    />

    <!-- 登录弹窗 -->
    <JijianLoginModal
      v-model="showLoginModal"
      mode="login"
      @login-success="handleLoginSuccess"
    />

    <!-- 注册弹窗 -->
    <JijianLoginModal
      v-model="showRegisterModal"
      mode="register"
      @register-success="handleRegisterSuccess"
    />

    <!-- 主要内容区域 -->
    <main class="main-content pt-24">
      <!-- 页面标题区域 -->
      <div class="upload-header bg-white shadow-sm">
        <div class="max-w-4xl mx-auto px-4 py-8">
          <div class="text-center">
            <h1 class="font-bold text-[var(--text-dark)] mb-4 flex items-center justify-center" style="font-size: 3.75rem;">
              <el-icon class="mr-3 text-[var(--primary)] text-4xl"><Upload /></el-icon>
              {{ $t('videoUpload.title') }}
            </h1>
            <p class="text-[var(--text-light)] max-w-2xl mx-auto" style="font-size: 1.25rem; line-height: 1.75rem;">
              {{ $t('videoUpload.supportedFormats') }}
            </p>
          </div>
        </div>
      </div>

      <div class="upload-content max-w-4xl mx-auto px-4 py-8">
        <!-- 处理警告提示 -->
        <div v-if="showProcessingWarning" class="processing-warning jijian-card p-6 mb-8 border-l-4 border-orange-500">
          <div class="flex items-start space-x-4">
            <div class="warning-icon">
              <el-icon class="text-2xl text-orange-500"><Warning /></el-icon>
            </div>
            <div class="warning-content">
              <h3 class="font-semibold text-[var(--text-dark)] mb-2">正在处理视频</h3>
              <p class="text-[var(--text-light)]">
                请勿关闭或切换页面，否则处理将中断。处理完成前请保持页面打开。
              </p>
            </div>
          </div>
        </div>

        <!-- WebCodecs 支持检查 -->
        <div v-if="!webCodecsSupported" class="support-warning jijian-card p-6 mb-8 border-l-4 border-yellow-500">
          <div class="flex items-start space-x-4">
            <div class="warning-icon">
              <el-icon class="text-2xl text-yellow-500"><InfoFilled /></el-icon>
            </div>
            <div class="warning-content">
              <h3 class="font-semibold text-[var(--text-dark)] mb-2">浏览器兼容性提示</h3>
              <p class="text-[var(--text-light)]">
                当前浏览器不支持 WebCodecs API，将使用基础处理模式。
                <br>推荐使用 Chrome 94+ 或 Edge 94+ 以获得最佳体验。
              </p>
            </div>
          </div>
        </div>

        <!-- 上传区域 -->
        <div
          class="upload-zone bg-white rounded-2xl shadow-[var(--shadow)] p-16 text-center border-2 border-dashed transition-all duration-300 cursor-pointer relative overflow-hidden"
          :class="[
            isDragging
              ? 'border-[var(--primary)] bg-[var(--primary)]/5 shadow-2xl scale-105'
              : 'border-gray-300 hover:border-[var(--primary)] hover:bg-gray-50 hover:shadow-xl'
          ]"
          @dragover.prevent="handleDragOver"
          @dragleave="handleDragLeave"
          @drop.prevent="handleDrop"
          @click="handleFileSelect"
        >
          <!-- 背景装饰 -->
          <div class="absolute top-0 right-0 w-32 h-32 bg-[var(--primary)]/5 rounded-full blur-2xl"></div>
          <div class="absolute bottom-0 left-0 w-24 h-24 bg-blue-500/5 rounded-full blur-xl"></div>

          <div class="upload-content-inner relative z-10">
            <!-- 上传图标 -->
            <div class="upload-icon-container mb-8">
              <div class="w-24 h-24 rounded-full bg-gradient-to-br from-[var(--primary)]/20 to-[var(--primary)]/10 flex items-center justify-center mx-auto mb-4 transform transition-transform duration-300 hover:scale-110">
                <el-icon class="text-5xl text-[var(--primary)]"><Upload /></el-icon>
              </div>
              <div class="flex justify-center space-x-2">
                <div class="w-2 h-2 bg-[var(--primary)] rounded-full animate-bounce" style="animation-delay: 0s"></div>
                <div class="w-2 h-2 bg-[var(--primary)] rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-[var(--primary)] rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
              </div>
            </div>

            <!-- 主标题 -->
            <h3 class="text-3xl font-bold text-[var(--text-dark)] mb-4">
              拖拽视频文件到此处
            </h3>

            <!-- 副标题 -->
            <p class="text-[var(--text-light)] mb-8 max-w-lg mx-auto text-lg">
              或点击选择文件开始您的AI视频剪辑之旅
            </p>

            <!-- 上传按钮 -->
            <el-button
              type="primary"
              size="large"
              class="upload-btn px-8 py-3 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
            >
              <i class="fa-solid fa-cloud-upload-alt mr-2"></i>
              选择视频文件
            </el-button>
            
            <!-- 支持格式提示 -->
            <div class="format-tips mt-8">
              <div class="flex flex-wrap justify-center gap-3">
                <div class="format-tag bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  MP4
                </div>
                <div class="format-tag bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  MOV
                </div>
                <div class="format-tag bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  AVI
                </div>
                <div class="format-tag bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  MKV
                </div>
                <div class="format-tag bg-red-100 text-red-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  WEBM
                </div>
              </div>
            </div>
            
            <p class="text-xs text-[var(--text-light)] mt-6">
              {{ $t('videoUpload.uploadLimit') }}
            </p>
          </div>
        </div>

        <!-- AI功能介绍 -->
        <div class="ai-features mt-20">
          <div class="text-center mb-12">
            <h3 class="text-3xl font-bold text-[var(--text-dark)] mb-4">
              <i class="fa-solid fa-magic-wand-sparkles text-[var(--primary)] mr-3"></i>
              AI智能剪辑功能
            </h3>
            <p class="text-[var(--text-light)] text-lg max-w-2xl mx-auto">
              上传视频后，AI将自动为您提供智能剪辑建议，让视频制作变得简单高效
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- 智能识别 -->
            <div class="feature-card bg-white rounded-xl p-8 shadow-[var(--shadow)] hover:shadow-xl transition-all duration-300 text-center group">
              <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <i class="fa-solid fa-eye text-2xl text-blue-600"></i>
              </div>
              <h4 class="text-xl font-semibold text-[var(--text-dark)] mb-3">智能识别</h4>
              <p class="text-[var(--text-light)]">AI自动识别视频中的人物、场景、动作，为精准剪辑提供基础</p>
            </div>

            <!-- 自动剪辑 -->
            <div class="feature-card bg-white rounded-xl p-8 shadow-[var(--shadow)] hover:shadow-xl transition-all duration-300 text-center group">
              <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <i class="fa-solid fa-scissors text-2xl text-green-600"></i>
              </div>
              <h4 class="text-xl font-semibold text-[var(--text-dark)] mb-3">自动剪辑</h4>
              <p class="text-[var(--text-light)]">根据内容特点自动生成剪辑方案，包括节奏控制和转场效果</p>
            </div>

            <!-- 智能配乐 -->
            <div class="feature-card bg-white rounded-xl p-8 shadow-[var(--shadow)] hover:shadow-xl transition-all duration-300 text-center group">
              <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <i class="fa-solid fa-music text-2xl text-purple-600"></i>
              </div>
              <h4 class="text-xl font-semibold text-[var(--text-dark)] mb-3">智能配乐</h4>
              <p class="text-[var(--text-light)]">AI分析视频情感和节奏，自动匹配合适的背景音乐和音效</p>
            </div>
          </div>
        </div>
      </div>
    </main>
    
    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-100 py-8 mt-16">
      <div class="max-w-4xl mx-auto px-4 text-center text-sm text-[var(--text-light)]">
        {{ $t('videoUpload.footerText') }}
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import {
  Upload,
  Warning,
  InfoFilled
} from '@element-plus/icons-vue'

// 导入组件
import JijianHeader from '@/components/jijian-ui/JijianHeader.vue'
import JijianLoginModal from '@/components/jijian-ui/JijianLoginModal.vue'

// 组合式API
const router = useRouter()
const { t } = useI18n()
const userStore = useUserStore()

// 响应式数据
const showLoginModal = ref(false)
const showRegisterModal = ref(false)
const isDragging = ref(false)
const showProcessingWarning = ref(false)
const webCodecsSupported = ref(true)

// 方法
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
}

const handleDragLeave = () => {
  isDragging.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = false
  const files = e.dataTransfer?.files
  if (files && files.length > 0) {
    handleFiles(files)
  }
}

const handleFileSelect = () => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'video/*'
  input.multiple = true
  input.onchange = (e) => {
    const target = e.target as HTMLInputElement
    if (target.files) {
      handleFiles(target.files)
    }
  }
  input.click()
}

const handleFiles = (files: FileList) => {
  console.log('Selected files:', files)
  // 这里保持原有的文件处理逻辑
  ElMessage.success(`选择了 ${files.length} 个文件`)
}



const handleLoginSuccess = (data: any) => {
  userStore.setUserInfo(data.user)
  userStore.setToken(data.token)
  userStore.setLoginStatus(true)
  ElMessage.success('登录成功！')
}

const handleRegisterSuccess = (data: any) => {
  userStore.setUserInfo(data.user)
  userStore.setLoginStatus(true)
  ElMessage.success('注册成功！')
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // 跳转到个人资料页面
      break
    case 'settings':
      // 跳转到设置页面
      break
    case 'logout':
      userStore.logout()
      ElMessage.success('已退出登录')
      break
  }
}

// 生命周期
onMounted(() => {
  // 检查WebCodecs支持
  webCodecsSupported.value = 'VideoDecoder' in window && 'VideoEncoder' in window
})
</script>

<style scoped>
/* 上传页面样式 */
.jijian-upload-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 上传区域样式 */
.upload-zone {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.upload-zone:hover {
  transform: translateY(-4px);
}

.upload-zone:hover .upload-icon-container {
  transform: scale(1.05);
}

.upload-zone.dragging {
  transform: scale(1.02) translateY(-4px);
  border-color: var(--primary);
  background: rgba(139, 92, 246, 0.05);
  box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
}

/* 上传按钮样式 */
.upload-btn {
  background: linear-gradient(135deg, var(--primary) 0%, #7c3aed 100%);
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-btn:hover {
  background: linear-gradient(135deg, #5b21b6 0%, #6d28d9 100%);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

/* 格式标签样式 */
.format-tag {
  transition: all 0.3s ease;
  cursor: pointer;
}

.format-tag:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 功能卡片样式 */
.feature-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid transparent;
}

.feature-card:hover {
  transform: translateY(-8px);
  border-color: var(--primary);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 动画效果 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.animate-bounce {
  animation: bounce 2s infinite;
}

/* 处理警告样式 */
.processing-warning {
  border-left: 4px solid #f59e0b;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-zone {
    padding: 2rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .format-tips {
    margin-top: 1.5rem;
  }
}
</style>
