<template>
  <div class="jijian-upload-page min-h-screen bg-jijian-bg-light">
    <!-- 顶部导航 -->
    <JijianHeader
      :is-authenticated="userStore.isLoggedIn"
      :user-name="userStore.userName"
      :user-avatar="userStore.userAvatar"
      @login-click="showLoginModal = true"
      @register-click="showRegisterModal = true"
      @upload-click="() => {}"
      @user-command="handleUserCommand"
    />

    <!-- 登录弹窗 -->
    <JijianLoginModal
      v-model="showLoginModal"
      mode="login"
      @login-success="handleLoginSuccess"
    />

    <!-- 注册弹窗 -->
    <JijianLoginModal
      v-model="showRegisterModal"
      mode="register"
      @register-success="handleRegisterSuccess"
    />

    <!-- 主要内容区域 -->
    <main class="main-content pt-24">
      <!-- 页面标题区域 -->
      <div class="upload-header bg-white shadow-sm">
        <div class="max-w-4xl mx-auto px-4 py-8">
          <div class="text-center">
            <h1 class="text-3xl md:text-4xl font-bold text-jijian-text-dark mb-4 flex items-center justify-center">
              <el-icon class="mr-3 text-jijian-primary text-4xl"><Upload /></el-icon>
              {{ $t('videoUpload.title') }}
            </h1>
            <p class="text-xl text-jijian-text-light max-w-2xl mx-auto">
              {{ $t('videoUpload.supportedFormats') }}
            </p>
          </div>
        </div>
      </div>

      <div class="upload-content max-w-4xl mx-auto px-4 py-8">
        <!-- 处理警告提示 -->
        <div v-if="showProcessingWarning" class="processing-warning jijian-card p-6 mb-8 border-l-4 border-orange-500">
          <div class="flex items-start space-x-4">
            <div class="warning-icon">
              <el-icon class="text-2xl text-orange-500"><Warning /></el-icon>
            </div>
            <div class="warning-content">
              <h3 class="font-semibold text-jijian-text-dark mb-2">正在处理视频</h3>
              <p class="text-jijian-text-light">
                请勿关闭或切换页面，否则处理将中断。处理完成前请保持页面打开。
              </p>
            </div>
          </div>
        </div>

        <!-- WebCodecs 支持检查 -->
        <div v-if="!webCodecsSupported" class="support-warning jijian-card p-6 mb-8 border-l-4 border-yellow-500">
          <div class="flex items-start space-x-4">
            <div class="warning-icon">
              <el-icon class="text-2xl text-yellow-500"><InfoFilled /></el-icon>
            </div>
            <div class="warning-content">
              <h3 class="font-semibold text-jijian-text-dark mb-2">浏览器兼容性提示</h3>
              <p class="text-jijian-text-light">
                当前浏览器不支持 WebCodecs API，将使用基础处理模式。
                <br>推荐使用 Chrome 94+ 或 Edge 94+ 以获得最佳体验。
              </p>
            </div>
          </div>
        </div>

        <!-- 上传区域 -->
        <div 
          class="upload-zone jijian-card p-12 text-center border-2 border-dashed transition-all duration-300 cursor-pointer"
          :class="[
            isDragging 
              ? 'border-jijian-primary bg-jijian-primary/5 shadow-lg' 
              : 'border-gray-300 hover:border-jijian-primary hover:bg-gray-50'
          ]"
          @dragover.prevent="handleDragOver"
          @dragleave="handleDragLeave"
          @drop.prevent="handleDrop"
          @click="handleFileSelect"
        >
          <div class="upload-content-inner">
            <div class="upload-icon w-20 h-20 rounded-full bg-jijian-primary/10 flex items-center justify-center mx-auto mb-6">
              <el-icon class="text-4xl text-jijian-primary"><CloudUpload /></el-icon>
            </div>
            
            <h3 class="text-2xl font-semibold text-jijian-text-dark mb-3">
              {{ $t('videoUpload.dragDrop') }}
            </h3>
            
            <p class="text-jijian-text-light mb-8 max-w-md mx-auto">
              {{ $t('videoUpload.supportedFormats') }}
            </p>
            
            <el-button 
              type="primary"
              size="large"
              class="jijian-btn jijian-btn-primary mb-8"
            >
              {{ $t('videoUpload.selectFile') }}
            </el-button>
            
            <!-- 云存储选项 -->
            <div class="cloud-storage-options">
              <div class="divider flex items-center mb-6">
                <div class="flex-1 h-px bg-gray-200"></div>
                <span class="px-4 text-jijian-text-light text-sm">或从云存储导入</span>
                <div class="flex-1 h-px bg-gray-200"></div>
              </div>
              
              <div class="flex flex-wrap justify-center gap-4">
                <el-button 
                  class="cloud-btn"
                  @click="handleCloudImport('google')"
                >
                  <el-icon class="mr-2"><Platform /></el-icon> Google Drive
                </el-button>
                <el-button 
                  class="cloud-btn"
                  @click="handleCloudImport('dropbox')"
                >
                  <el-icon class="mr-2"><Box /></el-icon> Dropbox
                </el-button>
                <el-button 
                  class="cloud-btn"
                  @click="handleCloudImport('onedrive')"
                >
                  <el-icon class="mr-2"><Folder /></el-icon> {{ $t('videoUpload.cloudStorage') }}
                </el-button>
              </div>
            </div>
            
            <p class="text-xs text-jijian-text-light mt-6">
              {{ $t('videoUpload.uploadLimit') }}
            </p>
          </div>
        </div>

        <!-- 上传历史 -->
        <div class="upload-history mt-16">
          <div class="flex justify-between items-center mb-8">
            <h3 class="text-2xl font-semibold text-jijian-text-dark">
              {{ $t('videoUpload.previousUploads') }}
            </h3>
            <el-button text type="primary">查看全部</el-button>
          </div>
          
          <div class="history-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            <!-- 示例上传项 -->
            <div class="history-item jijian-card overflow-hidden hover:shadow-xl transition-all duration-300 group cursor-pointer">
              <div class="relative aspect-video bg-gray-100">
                <img 
                  src="https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Video%20thumbnail&sign=0062cff261c08ff3564c2c087c5b6fa0" 
                  alt="Video thumbnail" 
                  class="w-full h-full object-cover"
                />
                <div class="overlay absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <el-button 
                    circle 
                    size="large"
                    class="play-btn bg-white/90 hover:bg-white"
                  >
                    <el-icon class="text-jijian-primary"><VideoPlay /></el-icon>
                  </el-button>
                </div>
                <div class="duration-badge absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                  04:23
                </div>
              </div>
              <div class="p-4">
                <h4 class="font-medium text-jijian-text-dark text-sm truncate mb-2">
                  {{ $t('videoUpload.sampleVideo') }}
                </h4>
                <p class="text-xs text-jijian-text-light">
                  {{ $t('videoUpload.uploadedOn') }} 2023-07-20
                </p>
              </div>
            </div>
            
            <!-- 空状态提示 -->
            <div class="upload-new-item jijian-card border-2 border-dashed border-jijian-primary/30 hover:border-jijian-primary hover:bg-jijian-primary/5 aspect-video flex flex-col items-center justify-center text-jijian-text-light cursor-pointer transition-all duration-300">
              <el-icon class="text-3xl mb-3 text-jijian-primary"><Plus /></el-icon>
              <span class="text-sm font-medium">{{ $t('videoUpload.uploadNew') }}</span>
            </div>
          </div>
        </div>
      </div>
    </main>
    
    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-100 py-8 mt-16">
      <div class="max-w-4xl mx-auto px-4 text-center text-sm text-jijian-text-light">
        {{ $t('videoUpload.footerText') }}
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import { 
  Upload, 
  Warning, 
  InfoFilled, 
  CloudUpload, 
  Platform, 
  Box, 
  Folder, 
  VideoPlay, 
  Plus 
} from '@element-plus/icons-vue'

// 导入组件
import JijianHeader from '@/components/jijian-ui/JijianHeader.vue'
import JijianLoginModal from '@/components/jijian-ui/JijianLoginModal.vue'

// 组合式API
const router = useRouter()
const { t } = useI18n()
const userStore = useUserStore()

// 响应式数据
const showLoginModal = ref(false)
const showRegisterModal = ref(false)
const isDragging = ref(false)
const showProcessingWarning = ref(false)
const webCodecsSupported = ref(true)

// 方法
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
}

const handleDragLeave = () => {
  isDragging.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = false
  const files = e.dataTransfer?.files
  if (files && files.length > 0) {
    handleFiles(files)
  }
}

const handleFileSelect = () => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'video/*'
  input.multiple = true
  input.onchange = (e) => {
    const target = e.target as HTMLInputElement
    if (target.files) {
      handleFiles(target.files)
    }
  }
  input.click()
}

const handleFiles = (files: FileList) => {
  console.log('Selected files:', files)
  // 这里保持原有的文件处理逻辑
  ElMessage.success(`选择了 ${files.length} 个文件`)
}

const handleCloudImport = (provider: string) => {
  ElMessage.info(`正在连接到 ${provider}...`)
  // 这里实现云存储导入逻辑
}

const handleLoginSuccess = (data: any) => {
  userStore.setUserInfo(data.user)
  userStore.setToken(data.token)
  userStore.setLoginStatus(true)
  ElMessage.success('登录成功！')
}

const handleRegisterSuccess = (data: any) => {
  userStore.setUserInfo(data.user)
  userStore.setLoginStatus(true)
  ElMessage.success('注册成功！')
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // 跳转到个人资料页面
      break
    case 'settings':
      // 跳转到设置页面
      break
    case 'logout':
      userStore.logout()
      ElMessage.success('已退出登录')
      break
  }
}

// 生命周期
onMounted(() => {
  // 检查WebCodecs支持
  webCodecsSupported.value = 'VideoDecoder' in window && 'VideoEncoder' in window
})
</script>

<style scoped>
.upload-zone:hover .upload-icon {
  transform: scale(1.1);
  background-color: var(--primary);
}

.upload-zone:hover .upload-icon .el-icon {
  color: white;
}

.cloud-btn {
  border: 1px solid var(--bg-light);
  color: var(--text-light);
  transition: all 0.3s ease;
}

.cloud-btn:hover {
  border-color: var(--primary);
  color: var(--primary);
  transform: translateY(-2px);
}

.history-item:hover {
  transform: translateY(-4px);
}

.upload-new-item:hover {
  transform: translateY(-4px);
}

.duration-badge {
  backdrop-filter: blur(4px);
}

.play-btn {
  transition: all 0.3s ease;
}

.play-btn:hover {
  transform: scale(1.1);
}
</style>
