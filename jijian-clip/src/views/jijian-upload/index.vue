<template>
  <div class="jijian-upload-page min-h-screen bg-[var(--bg-light)]">
    <!-- 顶部导航 -->
    <JijianHeader
      :is-authenticated="userStore.isLoggedIn"
      :user-name="userStore.userName"
      :user-avatar="userStore.userAvatar"
      @login-click="showLoginModal = true"
      @register-click="showRegisterModal = true"
      @upload-click="() => {}"
      @user-command="handleUserCommand"
    />

    <!-- 登录弹窗 -->
    <JijianLoginModal
      v-model="showLoginModal"
      mode="login"
      @login-success="handleLoginSuccess"
    />

    <!-- 注册弹窗 -->
    <JijianLoginModal
      v-model="showRegisterModal"
      mode="register"
      @register-success="handleRegisterSuccess"
    />

    <!-- 主要内容区域 -->
    <main class="main-content pt-24">
      <!-- 页面标题区域 -->
      <div class="upload-header bg-white shadow-sm">
        <div class="max-w-4xl mx-auto px-4 py-8">
          <div class="text-center">
            <h1 class="font-bold text-[var(--text-dark)] mb-4 flex items-center justify-center" style="font-size: 3.75rem;">
              <el-icon class="mr-3 text-[var(--primary)] text-4xl"><Upload /></el-icon>
              {{ $t('videoUpload.title') }}
            </h1>
            <p class="text-[var(--text-light)] max-w-2xl mx-auto" style="font-size: 1.25rem; line-height: 1.75rem;">
              {{ $t('videoUpload.supportedFormats') }}
            </p>
          </div>
        </div>
      </div>

      <div class="upload-content max-w-4xl mx-auto px-4 py-8">
        <!-- 处理警告提示 -->
        <div v-if="showProcessingWarning" class="processing-warning jijian-card p-6 mb-8 border-l-4 border-orange-500">
          <div class="flex items-start space-x-4">
            <div class="warning-icon">
              <el-icon class="text-2xl text-orange-500"><Warning /></el-icon>
            </div>
            <div class="warning-content">
              <h3 class="font-semibold text-[var(--text-dark)] mb-2">正在处理视频</h3>
              <p class="text-[var(--text-light)]">
                请勿关闭或切换页面，否则处理将中断。处理完成前请保持页面打开。
              </p>
            </div>
          </div>
        </div>

        <!-- WebCodecs 支持检查 -->
        <div v-if="!webCodecsSupported" class="support-warning jijian-card p-6 mb-8 border-l-4 border-yellow-500">
          <div class="flex items-start space-x-4">
            <div class="warning-icon">
              <el-icon class="text-2xl text-yellow-500"><InfoFilled /></el-icon>
            </div>
            <div class="warning-content">
              <h3 class="font-semibold text-[var(--text-dark)] mb-2">浏览器兼容性提示</h3>
              <p class="text-[var(--text-light)]">
                当前浏览器不支持 WebCodecs API，将使用基础处理模式。
                <br>推荐使用 Chrome 94+ 或 Edge 94+ 以获得最佳体验。
              </p>
            </div>
          </div>
        </div>

        <!-- 上传区域 -->
        <div
          class="upload-zone bg-white rounded-2xl shadow-[var(--shadow)] p-16 text-center border-2 border-dashed transition-all duration-300 cursor-pointer relative overflow-hidden"
          :class="[
            isDragging
              ? 'border-[var(--primary)] bg-[var(--primary)]/5 shadow-2xl scale-105'
              : 'border-gray-300 hover:border-[var(--primary)] hover:bg-gray-50 hover:shadow-xl'
          ]"
          @dragover.prevent="handleDragOver"
          @dragleave="handleDragLeave"
          @drop.prevent="handleDrop"
          @click="handleFileSelect"
        >
          <!-- 背景装饰 -->
          <div class="absolute top-0 right-0 w-32 h-32 bg-[var(--primary)]/5 rounded-full blur-2xl"></div>
          <div class="absolute bottom-0 left-0 w-24 h-24 bg-blue-500/5 rounded-full blur-xl"></div>

          <div class="upload-content-inner relative z-10">
            <!-- 上传图标 -->
            <div class="upload-icon-container mb-8">
              <div class="w-24 h-24 rounded-full bg-gradient-to-br from-[var(--primary)]/20 to-[var(--primary)]/10 flex items-center justify-center mx-auto mb-4 transform transition-transform duration-300 hover:scale-110">
                <el-icon class="text-5xl text-[var(--primary)]"><Upload /></el-icon>
              </div>
              <div class="flex justify-center space-x-2">
                <div class="w-2 h-2 bg-[var(--primary)] rounded-full animate-bounce" style="animation-delay: 0s"></div>
                <div class="w-2 h-2 bg-[var(--primary)] rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-[var(--primary)] rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
              </div>
            </div>

            <!-- 主标题 -->
            <h3 class="text-3xl font-bold text-[var(--text-dark)] mb-4">
              拖拽视频文件到此处
            </h3>

            <!-- 副标题 -->
            <p class="text-[var(--text-light)] mb-8 max-w-lg mx-auto text-lg">
              或点击选择文件开始您的AI视频剪辑之旅
            </p>

            <!-- 上传按钮 -->
            <el-button
              type="primary"
              size="large"
              class="upload-btn px-8 py-3 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
            >
              <i class="fa-solid fa-cloud-upload-alt mr-2"></i>
              选择视频文件
            </el-button>
            
            <!-- 支持格式提示 -->
            <div class="format-tips mt-8">
              <div class="flex flex-wrap justify-center gap-3">
                <div class="format-tag bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  MP4
                </div>
                <div class="format-tag bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  MOV
                </div>
                <div class="format-tag bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  AVI
                </div>
                <div class="format-tag bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  MKV
                </div>
                <div class="format-tag bg-red-100 text-red-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  WEBM
                </div>
              </div>
            </div>
            
            <p class="text-xs text-[var(--text-light)] mt-6">
              {{ $t('videoUpload.uploadLimit') }}
            </p>
          </div>
        </div>

        <!-- 最近使用项目展示 -->
        <div class="recent-projects mt-16">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-semibold text-[var(--text-dark)]">
              <i class="fa-solid fa-clock-rotate-left text-[var(--primary)] mr-2"></i>
              最近使用
            </h3>
            <el-button
              text
              type="primary"
              @click="goToProjectList"
              class="view-all-btn"
              size="small"
            >
              查看全部
              <i class="fa-solid fa-arrow-right ml-1"></i>
            </el-button>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="loading-state text-center py-8">
            <div class="inline-flex items-center space-x-2 text-[var(--text-light)]">
              <div class="w-4 h-4 border-2 border-[var(--primary)]/20 border-t-[var(--primary)] rounded-full animate-spin"></div>
              <span class="text-sm">加载中...</span>
            </div>
          </div>

          <!-- 项目预览卡片网格 -->
          <div v-else-if="recentProjects.length > 0" class="projects-grid grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            <div
              v-for="project in recentProjects"
              :key="project.id"
              class="project-card bg-white rounded-xl shadow-sm hover:shadow-lg border border-gray-100 hover:border-[var(--primary)]/30 transition-all duration-300 cursor-pointer group overflow-hidden"
              @click="openProject(project)"
            >
              <!-- 项目预览缩略图 -->
              <div class="project-thumbnail relative aspect-video bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
                <!-- 如果有缩略图就显示 -->
                <img
                  v-if="project.thumbnail"
                  :src="project.thumbnail"
                  :alt="project.name"
                  class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  @error="handleImageError"
                />
                <!-- 没有缩略图时的占位符 -->
                <div v-else class="w-full h-full flex items-center justify-center">
                  <div class="text-center">
                    <div class="w-12 h-12 bg-[var(--primary)]/10 rounded-full flex items-center justify-center mx-auto mb-2">
                      <i class="fa-solid fa-video text-[var(--primary)] text-xl"></i>
                    </div>
                    <span class="text-xs text-gray-400 font-medium">视频项目</span>
                  </div>
                </div>

                <!-- 悬停播放按钮 -->
                <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <div class="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center shadow-lg">
                    <i class="fa-solid fa-play text-[var(--primary)] ml-0.5"></i>
                  </div>
                </div>

                <!-- AI标识 -->
                <div class="absolute top-2 left-2">
                  <div class="bg-[var(--primary)]/90 text-white text-xs px-2 py-0.5 rounded-full font-medium">
                    AI
                  </div>
                </div>
              </div>

              <!-- 项目信息 -->
              <div class="project-info p-3">
                <h4 class="project-name font-medium text-[var(--text-dark)] text-sm truncate mb-1 group-hover:text-[var(--primary)] transition-colors">
                  {{ project.name }}
                </h4>
                <p class="project-time text-xs text-[var(--text-light)]">
                  {{ formatRelativeTime(project.updateTime) }}
                </p>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state text-center py-12">
            <div class="w-20 h-20 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-6">
              <i class="fa-solid fa-folder-open text-3xl text-gray-400"></i>
            </div>
            <h4 class="text-lg font-semibold text-[var(--text-dark)] mb-3">还没有项目</h4>
            <p class="text-[var(--text-light)] mb-6">上传您的第一个视频开始创作吧！</p>
            <el-button
              type="primary"
              @click="handleFileSelect"
              size="large"
              class="create-first-btn"
            >
              <i class="fa-solid fa-plus mr-2"></i>
              创建第一个项目
            </el-button>
          </div>
        </div>
      </div>
    </main>
    
    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-100 py-8 mt-16">
      <div class="max-w-4xl mx-auto px-4 text-center text-sm text-[var(--text-light)]">
        {{ $t('videoUpload.footerText') }}
      </div>
    </footer>

    <!-- 文件处理状态覆盖层 -->
    <div v-if="isProcessing" class="processing-overlay fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div class="processing-modal bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
        <div class="text-center">
          <!-- 处理图标 -->
          <div class="w-16 h-16 bg-[var(--primary)]/10 rounded-full flex items-center justify-center mx-auto mb-6">
            <div class="w-8 h-8 border-4 border-[var(--primary)]/20 border-t-[var(--primary)] rounded-full animate-spin"></div>
          </div>

          <!-- 处理状态 -->
          <div v-if="processingStatus">
            <h3 class="text-xl font-semibold text-[var(--text-dark)] mb-2">
              {{ processingStatus.title }}
            </h3>
            <p class="text-[var(--text-light)] mb-6">
              {{ processingStatus.description }}
            </p>
          </div>

          <!-- 进度条 -->
          <div class="progress-container mb-4">
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="bg-[var(--primary)] h-2 rounded-full transition-all duration-300"
                :style="{ width: processingProgress + '%' }"
              ></div>
            </div>
            <div class="text-sm text-[var(--text-light)] mt-2">
              {{ processingProgress }}%
            </div>
          </div>

          <!-- 提示信息 -->
          <p class="text-xs text-[var(--text-light)]">
            请勿关闭页面，正在处理您的视频...
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import {
  Upload,
  Warning,
  InfoFilled
} from '@element-plus/icons-vue'
import { db, type Project } from '@/db/db'
import { getFileUrl } from '@/utils/opfs-file'
import { write } from 'opfs-tools'
import { type Track, type TrackClip } from '@/types/track'
import dayjs from 'dayjs'

// 导入组件
import JijianHeader from '@/components/jijian-ui/JijianHeader.vue'
import JijianLoginModal from '@/components/jijian-ui/JijianLoginModal.vue'

// 组合式API
const router = useRouter()
const { t } = useI18n()
const userStore = useUserStore()

// 响应式数据
const showLoginModal = ref(false)
const showRegisterModal = ref(false)
const isDragging = ref(false)
const showProcessingWarning = ref(false)
const webCodecsSupported = ref(true)

// 项目相关数据
const loading = ref(false)
const recentProjects = ref<Project[]>([])
const maxRecentProjects = 5 // 最多显示5个最近项目

// 文件上传相关数据
const selectedFile = ref<File | null>(null)
const isProcessing = ref(false)
const processingProgress = ref(0)
const processingStatus = ref<{title: string, description: string} | null>(null)

// 方法
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
}

const handleDragLeave = () => {
  isDragging.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = false
  const files = e.dataTransfer?.files
  if (files && files.length > 0) {
    handleFiles(files)
  }
}

const handleFileSelect = () => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'video/*'
  input.multiple = true
  input.onchange = (e) => {
    const target = e.target as HTMLInputElement
    if (target.files) {
      handleFiles(target.files)
    }
  }
  input.click()
}

const handleFiles = async (files: FileList) => {
  if (files.length === 0) return

  const file = files[0]

  // 检查文件类型
  if (!file.type.startsWith('video/')) {
    ElMessage.error('请选择视频文件')
    return
  }

  // 检查文件大小 (限制为500MB)
  const maxSize = 500 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过500MB')
    return
  }

  selectedFile.value = file
  console.log('选择的文件:', file.name, '大小:', formatFileSize(file.size))

  // 开始处理文件
  await processVideoFile(file)
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 处理视频文件
const processVideoFile = async (file: File) => {
  try {
    isProcessing.value = true
    processingProgress.value = 0

    // 步骤1: 保存文件到OPFS
    processingStatus.value = {
      title: '保存文件',
      description: '正在将文件保存到本地存储...'
    }
    processingProgress.value = 20

    await saveFileToOPFS(file)

    // 步骤2: 创建项目
    processingStatus.value = {
      title: '创建项目',
      description: '正在创建新的视频项目...'
    }
    processingProgress.value = 60

    const projectId = await createVideoProject(file)

    // 步骤3: 完成
    processingStatus.value = {
      title: '准备完成',
      description: '即将跳转到编辑页面...'
    }
    processingProgress.value = 100

    // 等待一下让用户看到完成状态
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 跳转到编辑页面
    await router.push(`/clip/${projectId}`)

  } catch (error) {
    console.error('处理视频文件失败:', error)
    ElMessage.error('处理失败: ' + (error instanceof Error ? error.message : '未知错误'))
  } finally {
    isProcessing.value = false
    processingStatus.value = null
    processingProgress.value = 0
  }
}

// 保存文件到OPFS
const saveFileToOPFS = async (file: File) => {
  try {
    const filePath = `/video/${file.name}`
    console.log('💾 开始保存文件到 OPFS:', filePath)

    // 使用 opfs-tools 写入文件
    await write(filePath, file.stream())

    console.log('✅ 文件已保存到 OPFS:', filePath)
  } catch (error) {
    console.error('❌ 保存文件到 OPFS 失败:', error)
    throw new Error('文件保存失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

// 创建视频项目
const createVideoProject = async (file: File): Promise<string> => {
  try {
    const projectId = generateProjectId()
    const now = Date.now()

    // 创建项目数据
    const project: Project = {
      id: projectId,
      name: file.name.replace(/\.[^/.]+$/, ''), // 移除文件扩展名
      createTime: now,
      updateTime: now,
      tracks: [
        {
          id: 'video-track-1',
          clips: [
            {
              id: 'clip-1',
              name: file.name,
              startTime: 0,
              duration: 0, // 实际时长需要视频解析后获取
              endTime: 0,
              path: `/video/${file.name}`,
              type: 'video'
            }
          ]
        }
      ]
    }

    // 保存到数据库
    await db.projects.add(project)

    console.log('✅ 项目创建成功:', projectId)

    // 刷新项目列表
    await loadRecentProjects()

    return projectId
  } catch (error) {
    console.error('❌ 创建项目失败:', error)
    throw new Error('项目创建失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

// 生成项目ID
const generateProjectId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2)
}



const handleLoginSuccess = (data: any) => {
  userStore.setUserInfo(data.user)
  userStore.setToken(data.token)
  userStore.setLoginStatus(true)
  ElMessage.success('登录成功！')
}

const handleRegisterSuccess = (data: any) => {
  userStore.setUserInfo(data.user)
  userStore.setLoginStatus(true)
  ElMessage.success('注册成功！')
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // 跳转到个人资料页面
      break
    case 'settings':
      // 跳转到设置页面
      break
    case 'logout':
      userStore.logout()
      ElMessage.success('已退出登录')
      break
  }
}

// 项目相关方法 - 支持缩略图预览
const loadRecentProjects = async () => {
  try {
    loading.value = true
    // 获取基本项目信息
    const allProjects = await db.projects
      .orderBy('updateTime')
      .reverse()
      .limit(maxRecentProjects)
      .toArray()

    // 异步加载缩略图，不阻塞界面显示
    recentProjects.value = allProjects

    // 后台加载缩略图
    loadProjectThumbnails(allProjects)
  } catch (error) {
    console.error('Failed to load recent projects:', error)
    ElMessage.error('加载项目失败')
  } finally {
    loading.value = false
  }
}

// 异步加载项目缩略图
const loadProjectThumbnails = async (projects: Project[]) => {
  for (const project of projects) {
    if (project.thumbnail) {
      try {
        const thumbnailUrl = await getFileUrl(project.thumbnail)
        // 更新对应项目的缩略图
        const index = recentProjects.value.findIndex(p => p.id === project.id)
        if (index !== -1) {
          recentProjects.value[index].thumbnail = thumbnailUrl
        }
      } catch (error) {
        console.warn('Failed to load thumbnail for project:', project.id, error)
        // 缩略图加载失败时保持为undefined，会显示占位符
      }
    }
  }
}



// 轻量级时间格式化 - 相对时间显示
const formatRelativeTime = (timestamp?: number) => {
  if (!timestamp) return '未知时间'

  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`

  // 超过一周显示具体日期
  return dayjs(timestamp).format('MM-DD')
}

const openProject = (project: Project) => {
  if (!project.id) {
    ElMessage.error('项目ID无效')
    return
  }

  try {
    // 跳转到编辑页面
    router.push(`/clip/${project.id}`)
  } catch (error) {
    console.error('Failed to navigate to project:', error)
    ElMessage.error('打开项目失败')
  }
}

const goToProjectList = () => {
  try {
    // 跳转到项目列表页面
    router.push('/projects')
  } catch (error) {
    console.error('Failed to navigate to project list:', error)
    ElMessage.error('跳转失败')
  }
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 图片加载失败时隐藏图片，显示占位符
  img.style.display = 'none'
}



// 生命周期
onMounted(() => {
  // 检查WebCodecs支持
  webCodecsSupported.value = 'VideoDecoder' in window && 'VideoEncoder' in window

  // 自动加载最近的项目
  loadRecentProjects()
})
</script>

<style scoped>
/* 上传页面样式 */
.jijian-upload-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 上传区域样式 */
.upload-zone {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.upload-zone:hover {
  transform: translateY(-4px);
}

.upload-zone:hover .upload-icon-container {
  transform: scale(1.05);
}

.upload-zone.dragging {
  transform: scale(1.02) translateY(-4px);
  border-color: var(--primary);
  background: rgba(139, 92, 246, 0.05);
  box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
}

/* 上传按钮样式 */
.upload-btn {
  background: linear-gradient(135deg, var(--primary) 0%, #7c3aed 100%);
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-btn:hover {
  background: linear-gradient(135deg, #5b21b6 0%, #6d28d9 100%);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

/* 格式标签样式 */
.format-tag {
  transition: all 0.3s ease;
  cursor: pointer;
}

.format-tag:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 项目预览卡片样式 */
.project-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.project-thumbnail {
  position: relative;
  overflow: hidden;
}

.project-thumbnail img {
  transition: transform 0.3s ease;
}

.project-card:hover .project-thumbnail img {
  transform: scale(1.05);
}

/* 默认提示样式 */
.default-hint {
  border: 1px dashed #e5e7eb;
  transition: all 0.3s ease;
}

.default-hint:hover {
  border-color: var(--primary);
  background-color: #fafafa;
}

/* 加载状态 */
.loading-state {
  opacity: 0.8;
}

/* 空状态样式 */
.empty-state {
  opacity: 0.9;
}

.create-first-btn {
  background: linear-gradient(135deg, var(--primary) 0%, #7c3aed 100%);
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.create-first-btn:hover {
  background: linear-gradient(135deg, #5b21b6 0%, #6d28d9 100%);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
  transform: translateY(-2px);
}

/* 按钮样式优化 */
.load-projects-btn {
  transition: all 0.2s ease;
}

.load-projects-btn:hover {
  transform: translateY(-1px);
}

.view-all-btn {
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  transform: translateX(2px);
}

/* 动画效果 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.animate-bounce {
  animation: bounce 2s infinite;
}

/* 处理警告样式 */
.processing-warning {
  border-left: 4px solid #f59e0b;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-zone {
    padding: 2rem;
  }

  .format-tips {
    margin-top: 1.5rem;
  }

  .recent-projects h3 {
    font-size: 1.25rem;
  }

  .projects-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .project-card {
    margin-bottom: 0.5rem;
  }

  .project-info {
    padding: 0.5rem;
  }

  .default-hint {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .projects-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .project-card {
    max-width: 280px;
    margin: 0 auto 0.75rem;
  }
}
</style>
