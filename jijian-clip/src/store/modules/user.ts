/**
 * 用户状态管理
 * 处理用户登录、注册、登出等操作
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

// 用户信息接口
export interface UserInfo {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  createdAt: string;
  lastLoginAt: string;
}

// 登录参数接口
export interface LoginParams {
  email: string;
  password: string;
  remember?: boolean;
}

// 注册参数接口
export interface RegisterParams {
  username: string;
  email: string;
  password: string;
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null);
  const token = ref<string>('');
  const isLoggedIn = ref<boolean>(false);

  // 计算属性
  const userName = computed(() => userInfo.value?.username || '');
  const userEmail = computed(() => userInfo.value?.email || '');
  const userAvatar = computed(() => userInfo.value?.avatar || '');

  // 从本地存储恢复用户状态
  const restoreUserState = () => {
    try {
      const savedToken = localStorage.getItem('jijian_token');
      const savedUserInfo = localStorage.getItem('jijian_user_info');
      
      if (savedToken && savedUserInfo) {
        token.value = savedToken;
        userInfo.value = JSON.parse(savedUserInfo);
        isLoggedIn.value = true;
      }
    } catch (error) {
      console.error('恢复用户状态失败:', error);
      clearUserState();
    }
  };

  // 清除用户状态
  const clearUserState = () => {
    userInfo.value = null;
    token.value = '';
    isLoggedIn.value = false;
    localStorage.removeItem('jijian_token');
    localStorage.removeItem('jijian_user_info');
  };

  // 保存用户状态到本地存储
  const saveUserState = (userToken: string, user: UserInfo, remember: boolean = false) => {
    token.value = userToken;
    userInfo.value = user;
    isLoggedIn.value = true;

    if (remember) {
      localStorage.setItem('jijian_token', userToken);
      localStorage.setItem('jijian_user_info', JSON.stringify(user));
    } else {
      // 如果不记住登录状态，使用sessionStorage
      sessionStorage.setItem('jijian_token', userToken);
      sessionStorage.setItem('jijian_user_info', JSON.stringify(user));
    }
  };

  // 登录
  const login = async (params: LoginParams): Promise<void> => {
    try {
      // 模拟API调用
      const response = await mockLoginAPI(params);
      
      if (response.success) {
        saveUserState(response.token, response.user, params.remember || false);
      } else {
        throw new Error(response.message || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  };

  // 注册
  const register = async (params: RegisterParams): Promise<void> => {
    try {
      // 模拟API调用
      const response = await mockRegisterAPI(params);
      
      if (response.success) {
        saveUserState(response.token, response.user, true);
      } else {
        throw new Error(response.message || '注册失败');
      }
    } catch (error) {
      console.error('注册失败:', error);
      throw error;
    }
  };

  // 登出
  const logout = async (): Promise<void> => {
    try {
      // 可以在这里调用登出API
      clearUserState();
    } catch (error) {
      console.error('登出失败:', error);
      // 即使API调用失败，也要清除本地状态
      clearUserState();
    }
  };

  // 更新用户信息
  const updateUserInfo = async (updates: Partial<UserInfo>): Promise<void> => {
    try {
      if (!userInfo.value) {
        throw new Error('用户未登录');
      }

      // 模拟API调用
      const response = await mockUpdateUserAPI(updates);
      
      if (response.success) {
        userInfo.value = { ...userInfo.value, ...updates };
        
        // 更新本地存储
        const savedToken = localStorage.getItem('jijian_token') || sessionStorage.getItem('jijian_token');
        if (savedToken) {
          if (localStorage.getItem('jijian_token')) {
            localStorage.setItem('jijian_user_info', JSON.stringify(userInfo.value));
          } else {
            sessionStorage.setItem('jijian_user_info', JSON.stringify(userInfo.value));
          }
        }
      } else {
        throw new Error(response.message || '更新用户信息失败');
      }
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  };

  // 检查登录状态
  const checkLoginStatus = async (): Promise<boolean> => {
    try {
      if (!token.value) {
        return false;
      }

      // 模拟API调用验证token
      const response = await mockCheckTokenAPI(token.value);
      
      if (!response.success) {
        clearUserState();
        return false;
      }

      return true;
    } catch (error) {
      console.error('检查登录状态失败:', error);
      clearUserState();
      return false;
    }
  };

  // 初始化用户状态
  const initUserState = async () => {
    restoreUserState();
    
    if (token.value) {
      const isValid = await checkLoginStatus();
      if (!isValid) {
        clearUserState();
      }
    }
  };

  // 新增方法：设置用户信息（用于新组件）
  const setUserInfo = (user: UserInfo) => {
    userInfo.value = user;
  };

  // 新增方法：设置token（用于新组件）
  const setToken = (userToken: string) => {
    token.value = userToken;
    localStorage.setItem('jijian_token', userToken);
  };

  // 新增方法：设置登录状态（用于新组件）
  const setLoginStatus = (status: boolean) => {
    isLoggedIn.value = status;
  };

  return {
    // 状态
    userInfo,
    token,
    isLoggedIn,

    // 计算属性
    userName,
    userEmail,
    userAvatar,

    // 方法
    login,
    register,
    logout,
    updateUserInfo,
    checkLoginStatus,
    initUserState,
    clearUserState,

    // 新增方法
    setUserInfo,
    setToken,
    setLoginStatus
  };
});

// 模拟API函数
const mockLoginAPI = async (params: LoginParams) => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 模拟登录验证
  if (params.email === '<EMAIL>' && params.password === '123456') {
    return {
      success: true,
      token: 'mock_token_' + Date.now(),
      user: {
        id: '1',
        username: 'Demo用户',
        email: params.email,
        avatar: '',
        createdAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString()
      }
    };
  } else {
    return {
      success: false,
      message: '邮箱或密码错误'
    };
  }
};

const mockRegisterAPI = async (params: RegisterParams) => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // 模拟注册成功
  return {
    success: true,
    token: 'mock_token_' + Date.now(),
    user: {
      id: Date.now().toString(),
      username: params.username,
      email: params.email,
      avatar: '',
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString()
    }
  };
};

const mockUpdateUserAPI = async (updates: Partial<UserInfo>) => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return {
    success: true,
    message: '更新成功'
  };
};

const mockCheckTokenAPI = async (token: string) => {
  // 测试环境：简化token验证，总是返回成功
  console.log('🧪 测试环境：跳过token验证，token:', token);

  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 100));

  // 测试环境下总是返回成功
  return {
    success: true,
    message: '测试环境token验证通过'
  };
};
