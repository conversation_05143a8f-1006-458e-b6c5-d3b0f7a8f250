// 视频处理协调器 - 管理解码、特征提取和音频处理三个独立线程

import { getAlgorithmProcessor, SegmentResult } from './algorithm-wasm.js';
import { getGoAlgorithmProcessor } from './algorithm-wasm-go.js';
import { AudioProcessor, ASRConfig, ASRResult, AudioProcessingCallbacks } from './audio-processor.js';
import { TaskManager, taskManager } from './task-manager.js';
import { FusionProcessor, FusionConfig, FusionResult } from './fusion.js';
import { MetadataBuilder, SegmentMetadataRequest, MetadataBuilderConfig, MetadataResponse } from './metadata-builder.js';
import { StreamingSegmentFrameUploader } from './segment-frame-uploader-streaming.js';
import { ChapterAggregator, ChapterAggregationCallbacks, ChapterAggregationResponse } from './chapter-aggregator.js';
import { VideoInfo } from './types.js';

// 特征提取器类型和接口
type FeatureExtractorType = 'opencv' | 'tensorflow';
type AlgorithmType = 'python' | 'go';

interface IFeatureExtractor {
  initialize(): Promise<void>;
  processFrame(frame: VideoFrame, frameIndex?: number): Promise<any>;
  dispose(): void;
  flush?(): Promise<void>; // 批处理器可能需要flush
  isBatchProcessor?: boolean; // 标识是否为批处理器
}

interface FrameQueueItem {
  videoFrame: VideoFrame;
  frameIndex: number;
  timestamp: number;
}

interface ProcessingCallbacks {
  onProgress: (frameCount: number, totalFrames: number) => void;
  onComplete: (stats: ProcessingStats) => void;
  onError: (error: Error) => void;
  onResume?: (pausedState: any) => void; // 可选的恢复回调
  onAudioProgress?: (status: string) => void; // 音频处理进度回调
  onAudioComplete?: (result: ASRResult) => void; // 音频处理完成回调
  onAudioError?: (error: Error) => void; // 音频处理错误回调
  onChapterProgress?: (status: string) => void; // 章节聚合进度回调
  onChapterComplete?: (result: ChapterAggregationResponse) => void; // 章节聚合完成回调
  onChapterError?: (error: Error) => void; // 章节聚合错误回调
}

interface FrameFeature {
  frameIndex: number;
  timestamp: number;
  colorDiff: number;
  motionDiff: number;
  textureDiff: number;
}

interface ProcessingStats {
  totalFrames: number;
  totalTime: number; // 秒
  avgTimePerFrame: number; // 毫秒
  decodingTime: number; // 秒
  featureExtractionTime: number; // 秒
  segmentationTime?: number; // 算法分段时间（秒）
  segmentTimes: number[]; // 分段点时间（秒）
  audioProcessingTime?: number; // 音频处理时间（秒）
  asrResult?: ASRResult; // ASR识别结果
  fusionResult?: FusionResult; // 融合结果
  segmentMetadata?: SegmentMetadataRequest; // 分段元数据
}

interface AlgorithmConfig {
  min_avg_duration: number;
  max_avg_duration: number;
  initial_pen: number;
  max_iterations: number;
  min_size: number;
}

// 线程安全的帧队列
class FrameQueue {
  private queue: FrameQueueItem[] = [];
  private maxSize: number = 100; // 最大队列大小，防止内存溢出

  enqueue(item: FrameQueueItem): boolean {
    if (this.queue.length >= this.maxSize) {
      console.warn('⚠️ 队列已满，跳过帧');
      return false;
    }
    this.queue.push(item);
    return true;
  }

  dequeue(): FrameQueueItem | null {
    return this.queue.shift() || null;
  }

  size(): number {
    return this.queue.length;
  }

  isEmpty(): boolean {
    return this.queue.length === 0;
  }

  clear(): void {
    // 清理所有VideoFrame
    this.queue.forEach(item => {
      try {
        item.videoFrame.close();
      } catch (e) {
        // 忽略已关闭的错误
      }
    });
    this.queue = [];
  }
}

// 视频处理协调器
export class VideoProcessingCoordinator {
  private frameQueue = new FrameQueue();
  private decoder: any = null; // WebCodecsVideoDecoder实例
  private featureExtractor: IFeatureExtractor | null = null;
  private audioProcessor: AudioProcessor | null = null;

  // 状态管理
  private isFeatureExtracting = false;
  private decodingComplete = false;
  private decodingFailed = false;
  private featureExtractionComplete = false;
  private audioProcessingComplete = false;
  private isAudioProcessing = false;
  private segmentationComplete = false;
  private isSegmentationProcessing = false;

  // 计时
  private startTime = 0;
  private decodingStartTime = 0;
  private decodingEndTime = 0;
  private featureStartTime = 0;
  private featureEndTime = 0;
  private audioStartTime = 0;
  private audioEndTime = 0;
  private segmentationStartTime = 0;
  private segmentationEndTime = 0;

  // 日志节流相关
  private lastQueueCheckLog = 0;
  private queueCheckLogInterval = 5000; // 5秒内最多打印一次队列检查日志

  // 算法配置
  private algorithmConfig: AlgorithmConfig = {
    min_avg_duration: 8,
    max_avg_duration: 50,
    initial_pen: 5,
    max_iterations: 20,
    min_size: 10
  };

  // 算法类型配置
  private algorithmType: AlgorithmType = 'python';

  // 统计
  private processedFrames = 0;
  private extractedFeatures = 0;
  private collectedFeatures: FrameFeature[] = []; // 收集的特征数据
  private asrResult: ASRResult | null = null; // ASR识别结果
  private segmentResult: SegmentResult | null = null; // 分段结果
  private taskId: string = ''; // 任务ID

  // 融合和元数据构建
  private fusionProcessor: FusionProcessor;
  private metadataBuilder: MetadataBuilder;

  // 分段帧上传器
  private segmentFrameUploader: StreamingSegmentFrameUploader | null = null;

  // 章节聚合器
  private chapterAggregator: ChapterAggregator | null = null;

  // 可见性管理
  private isPaused = false;
  private visibilityListener: { onHidden: () => void; onVisible: () => void } | null = null;

  // 暂停状态保存
  private pausedState: {
    file: File | null;
    taskId: string | null;
    targetFps: number | undefined;
    wasProcessing: boolean;
    extractorType: string;
    useBatch: boolean;
    batchSize: number;
  } | null = null;

  // 当前处理状态
  private currentFile: File | null = null;
  private currentTargetFps: number | undefined = undefined;
  private currentExtractorType: string | null = null;
  private currentUseBatch: boolean = true;
  private currentBatchSize: number = 8;

  private callbacks: ProcessingCallbacks;

  constructor(callbacks: ProcessingCallbacks) {
    this.callbacks = callbacks;
    this.setupVisibilityManagement();

    // 初始化融合和元数据构建器
    this.fusionProcessor = new FusionProcessor();
    this.metadataBuilder = new MetadataBuilder();

    // 立即初始化流式分段帧上传器
    console.log('🚀 协调器构造时初始化流式分段帧上传器...');
    try {
      // 创建默认配置（使用临时值，后续会更新）
      const uploadConfig = StreamingSegmentFrameUploader.getDefaultConfig('temp_task_id', 'temp_video_code');
      this.segmentFrameUploader = new StreamingSegmentFrameUploader(uploadConfig);
      console.log('✅ 流式分段帧上传器已创建');
    } catch (error) {
      console.warn('⚠️ 流式分段帧上传器初始化失败:', error);
      // 不阻塞构造函数，继续执行
    }

    // 初始化章节聚合器
    console.log('📚 协调器构造时初始化章节聚合器...');
    try {
      const chapterCallbacks: ChapterAggregationCallbacks = {
        onProgress: (status: string) => {
          console.log('📚 章节聚合进度:', status);
          if (this.callbacks.onChapterProgress) {
            this.callbacks.onChapterProgress(status);
          }
        },
        onComplete: (result: ChapterAggregationResponse) => {
          console.log('✅ 章节聚合完成:', result);
          if (this.callbacks.onChapterComplete) {
            this.callbacks.onChapterComplete(result);
          }
        },
        onError: (error: Error) => {
          console.error('❌ 章节聚合失败:', error);
          if (this.callbacks.onChapterError) {
            this.callbacks.onChapterError(error);
          }
        }
      };
      this.chapterAggregator = new ChapterAggregator(undefined, chapterCallbacks);
      console.log('✅ 章节聚合器已创建');
    } catch (error) {
      console.warn('⚠️ 章节聚合器初始化失败:', error);
      // 不阻塞构造函数，继续执行
    }
  }

  // 节流日志方法，避免频繁打印相同信息
  private throttledLog(message: string, data?: any, interval: number = 5000): void {
    const now = Date.now();
    if (now - this.lastQueueCheckLog > interval) {
      console.log(message, data);
      this.lastQueueCheckLog = now;
    }
  }

  // 设置页面可见性管理
  private setupVisibilityManagement(): void {
    this.visibilityListener = {
      onHidden: () => {
        console.log('⏸️ 页面隐藏，暂停视频处理并清理资源');
        this.pauseProcessing();
      },
      onVisible: () => {
        console.log('▶️ 页面可见，可以恢复处理');
        this.resumeProcessing();
      }
    };

    // 注册监听器（需要在HTML中引入visibility-manager）
    if (typeof window !== 'undefined' && (window as any).visibilityManager) {
      (window as any).visibilityManager.addListener(this.visibilityListener);
    }
  }

  // 暂停处理
  private pauseProcessing(): void {
    if (this.isPaused) return; // 已经暂停了

    console.log('⏸️ 开始优雅暂停处理...');
    this.isPaused = true;

    // 首先停止解码器，防止产生新的帧
    if (this.decoder) {
      console.log('🛑 停止解码器...');
      this.decoder.dispose();
      this.decoder = null;
    }

    // 保存当前状态（如果正在处理）
    if (this.isFeatureExtracting || this.isAudioProcessing || this.decoder) {
      this.pausedState = {
        file: this.currentFile || null,
        taskId: this.taskId || null,
        targetFps: this.currentTargetFps,
        wasProcessing: this.isFeatureExtracting || this.isAudioProcessing,
        extractorType: this.currentExtractorType || 'tensorflow',
        useBatch: this.currentUseBatch || true,
        batchSize: this.currentBatchSize || 8
      };
      console.log('💾 已保存处理状态:', this.pausedState);
    }

    // 等待一小段时间让正在处理的帧完成
    setTimeout(() => {
      // 清理队列中的所有VideoFrame
      console.log('🧹 清理帧队列...');
      this.frameQueue.clear();

      // 清理特征提取器中的资源
      if (this.featureExtractor && this.featureExtractor.dispose) {
        console.log('🧹 清理特征提取器...');
        this.featureExtractor.dispose();
      }

      console.log('✅ 暂停完成，所有资源已清理');
    }, 100); // 给正在处理的帧100ms时间完成
  }

  // 恢复处理
  private async resumeProcessing(): Promise<void> {
    if (!this.isPaused) return; // 没有暂停

    console.log('▶️ 页面恢复可见');
    this.isPaused = false;

    // 如果有保存的状态，询问用户是否重新处理
    if (this.pausedState && this.pausedState.wasProcessing) {
      console.log('🔄 检测到之前的处理状态，询问用户是否重新处理...');

      // 通过回调通知UI可以重新处理
      if (this.callbacks.onResume) {
        this.callbacks.onResume(this.pausedState);
      } else {
        // 如果没有恢复回调，自动重新处理
        await this.autoResume();
      }
    }
  }

  // 自动重新处理
  private async autoResume(): Promise<void> {
    if (!this.pausedState || !this.pausedState.file) {
      console.log('❌ 无法自动重新处理：缺少必要状态');
      return;
    }

    try {
      console.log('🚀 开始重新处理（从头开始）...');

      // 重新初始化特征提取器
      await this.initializeFeatureExtractor(
        this.pausedState.extractorType as any,
        this.pausedState.useBatch,
        this.pausedState.batchSize
      );

      // 重新开始处理（使用原来的taskId）
      // 注意：resume时使用保存的taskId，不需要重新创建任务
      const taskId = this.pausedState.taskId;
      if (!taskId) {
        throw new Error('缺少保存的任务ID，无法恢复处理');
      }
      await this.startProcessing(this.pausedState.file, taskId, this.pausedState.targetFps);

      console.log('✅ 重新处理成功');
    } catch (error) {
      console.error('❌ 重新处理失败:', error);
      this.callbacks.onError(error as Error);
    } finally {
      // 清理保存的状态
      this.pausedState = null;
    }
  }

  // 手动重新处理（供UI调用）
  async manualResume(): Promise<void> {
    if (!this.pausedState) {
      console.log('❌ 没有可重新处理的状态');
      return;
    }

    await this.autoResume();
  }

  // 取消重新处理（供UI调用）
  cancelResume(): void {
    if (this.pausedState) {
      console.log('❌ 用户取消重新处理');
      this.pausedState = null;
    }
  }

  // 设置算法配置
  setAlgorithmConfig(config: Partial<AlgorithmConfig>): void {
    this.algorithmConfig = { ...this.algorithmConfig, ...config };
    console.log('🔧 算法配置已更新:', this.algorithmConfig);
  }

  // 设置算法类型
  setAlgorithmType(type: AlgorithmType): void {
    this.algorithmType = type;
    console.log('🔧 算法类型已设置为:', type);
  }

  // 初始化特征提取器
  async initializeFeatureExtractor(type: FeatureExtractorType, useBatch: boolean = true, batchSize: number = 8): Promise<void> {
    try {
      // 保存特征提取器状态
      this.currentExtractorType = type;
      this.currentUseBatch = useBatch;
      this.currentBatchSize = batchSize;

      // 动态导入对应的特征提取器
      if (type === 'opencv') {
        const { FeatureExtractor } = await import('./feature-extractor-cv.js');
        this.featureExtractor = new FeatureExtractor();
      } else {
        if (useBatch) {
          const { BatchFeatureExtractor } = await import('./feature-extractor-tf-batch.js');
          this.featureExtractor = new BatchFeatureExtractor(batchSize);
          console.log(`🚀 使用批处理TensorFlow.js特征提取器，批大小: ${batchSize}`);
        } else {
          const { FeatureExtractor } = await import('./feature-extractor-tf.js');
          this.featureExtractor = new FeatureExtractor();
          console.log('🔄 使用单帧TensorFlow.js特征提取器');
        }
      }

      // 初始化特征提取器
      await this.featureExtractor.initialize();
      const extractorName = type === 'opencv' ? 'OpenCV.js' :
                           (useBatch ? 'TensorFlow.js (批处理)' : 'TensorFlow.js (单帧)');
      console.log(`✅ ${extractorName} 特征提取器已启用`);
    } catch (error) {
      console.error('❌ 特征提取器初始化失败:', error);

      // 🎯 如果 TensorFlow.js 初始化失败，尝试降级到 OpenCV.js
      if (type === 'tensorflow' && error.message.includes('tf is not defined')) {
        console.warn('⚠️ TensorFlow.js 不可用，尝试降级到 OpenCV.js');
        try {
          const { FeatureExtractor } = await import('./feature-extractor-cv');
          this.featureExtractor = new FeatureExtractor();
          await this.featureExtractor.initialize();
          console.log('✅ 已降级到 OpenCV.js 特征提取器');
          return; // 成功降级，直接返回
        } catch (fallbackError) {
          console.error('❌ OpenCV.js 降级也失败:', fallbackError);

          // 🎯 最终降级：使用基础特征提取器
          console.warn('⚠️ 使用基础特征提取器（无机器学习）');
          this.featureExtractor = new BasicFeatureExtractor();
          await this.featureExtractor.initialize();
          console.log('✅ 已降级到基础特征提取器');
          return;
        }
      }

      throw error;
    }
  }

  // 开始处理
  async startProcessing(file: File, taskId: string, targetFps?: number, enableAudio: boolean = true, asrConfig?: ASRConfig): Promise<void> {
    console.log('🎬 协调器开始处理...');
    if (targetFps) {
      console.log('🎯 目标FPS:', targetFps);
    }

    // 设置任务ID
    this.taskId = taskId;
    console.log('🆔 使用任务ID:', this.taskId);

    // 保存当前处理状态
    this.currentFile = file;
    this.currentTargetFps = targetFps;

    // 流式分段帧上传器已在构造函数中创建，准备就绪

    this.startTime = performance.now();
    this.processedFrames = 0;
    this.extractedFeatures = 0;
    this.collectedFeatures = []; // 重置特征数组
    this.asrResult = null; // 重置ASR结果
    this.segmentResult = null; // 重置分段结果
    this.decodingComplete = false;
    this.featureExtractionComplete = false;
    this.audioProcessingComplete = false; // 总是等待音频处理完成
    this.segmentationComplete = false; // 等待分段处理完成
    this.isAudioProcessing = false;
    this.isSegmentationProcessing = false;

    // 创建解码器
    await this.createDecoder();

    // 启动解码线程
    this.startDecodingThread(file, targetFps);

    // 启动特征提取线程
    if (this.featureExtractor) {
      this.startFeatureExtractionThread();
    } else {
      this.featureExtractionComplete = true;
    }

    // 总是启动音频处理线程，等待音频数据返回
    console.log('🎵 启动音频处理（等待音频数据返回）...');
    this.startAudioProcessingThread(file, asrConfig);
  }

  // 创建解码器
  private async createDecoder(): Promise<void> {
    const { WebCodecsVideoDecoder } = await import('./decoder.js');
    this.decoder = new WebCodecsVideoDecoder({
      onFrame: () => {}, // 将在startDecodingThread中重新设置
      onError: (error: Error) => this.callbacks.onError(error),
      onComplete: () => {} // 将在startDecodingThread中重新设置
    });

    await this.decoder.initialize();
    console.log('✅ 解码器已创建并初始化');
  }

  // 解码线程
  private async startDecodingThread(file: File, targetFps?: number): Promise<void> {
    console.log('🎬 启动解码线程...');
    this.decodingStartTime = performance.now();

    try {
      // 配置解码器回调
      this.decoder.callbacks = {
        onFrame: (yuvFrame: any) => {
          // 如果已暂停，立即关闭VideoFrame并返回
          if (this.isPaused) {
            console.log('⏸️ 已暂停，跳过帧:', this.processedFrames);
            try {
              yuvFrame.videoFrame.close();
            } catch (e) {
              // 忽略关闭错误
            }
            return;
          }

          console.log('🎬 解码器收到帧:', this.processedFrames);
          // 将帧放入队列 - 避免克隆VideoFrame以防止内存问题
          const queueItem: FrameQueueItem = {
            videoFrame: yuvFrame.videoFrame, // 直接使用VideoFrame，不克隆
            frameIndex: this.processedFrames,
            timestamp: yuvFrame.timestamp
          };

          if (this.frameQueue.enqueue(queueItem)) {
            this.processedFrames++;
            // 使用估算的总帧数进行进度更新
            const estimatedTotal = Math.max(this.processedFrames, 100);
            this.callbacks.onProgress(this.processedFrames, estimatedTotal);
          }
        },
        onError: (error: Error) => {
          console.error('❌ 解码错误:', error);

          // 🎯 视频解码失败时的降级处理
          if (error.message.includes('findPosition') ||
              error.message.includes('MP4Box') ||
              error.message.includes('视频track数据不完整') ||
              error.message.includes('H.265格式兼容性问题') ||
              error.message.includes('视频文件格式不兼容') ||
              error.message.includes('stsc表') ||
              error.message.includes('chunk偏移信息')) {
            console.warn('⚠️ 视频解码失败，但音频处理成功，继续使用音频分段');

            // 标记视频解码失败，但不中断整个流程
            this.decodingComplete = true;
            this.decodingFailed = true;

            // 检查是否可以仅基于音频完成处理
            this.checkAllComplete().catch(checkError => {
              console.error('❌ 完成检查失败:', checkError);
              this.callbacks.onError(new Error('视频解码失败，请尝试使用 H.264 格式的视频文件'));
            });
          } else {
            this.callbacks.onError(error);
          }
        },
        onComplete: () => {
          this.decodingEndTime = performance.now();
          this.decodingComplete = true;
          console.log('✅ 解码线程完成，总帧数:', this.processedFrames);
          console.log('🔍 解码完成时状态:', {
            queueSize: this.frameQueue.size(),
            isFeatureExtracting: this.isFeatureExtracting,
            featureExtractorExists: !!this.featureExtractor
          });
          this.checkAllComplete().catch(error => {
            console.error('❌ 完成检查失败:', error);
            this.callbacks.onError(error);
          });
        }
      };

      // 开始解码
      await this.decoder.decodeVideo(file, targetFps);

    } catch (error) {
      console.error('❌ 解码线程错误:', error);
      this.callbacks.onError(error as Error);
    }
  }

  // 特征提取线程
  private async startFeatureExtractionThread(): Promise<void> {
    console.log('🎯 启动特征提取线程...');
    this.isFeatureExtracting = true;
    this.featureStartTime = performance.now();

    // 持续处理队列中的帧
    const processQueue = async () => {
      console.log('🎯 特征提取队列处理开始...');

      while (this.isFeatureExtracting) {
        const item = this.frameQueue.dequeue();

        if (item && this.featureExtractor) {
          try {
            console.log(`🎯 处理特征提取 - 帧 ${item.frameIndex}`);

            // 检查是否为批处理器
            if (this.featureExtractor.isBatchProcessor) {
              // 批处理器：异步提交，不等待结果
              // 注意：批处理器内部会自动关闭VideoFrame，这里不需要关闭
              this.featureExtractor.processFrame(item.videoFrame, item.frameIndex)
                .then(features => {
                  this.extractedFeatures++;
                  if (features) {
                    // 保存特征数据
                    const frameFeature: FrameFeature = {
                      frameIndex: features.frameIndex,
                      timestamp: features.timestamp,
                      colorDiff: features.colorDiff,
                      motionDiff: features.motionDiff,
                      textureDiff: features.textureDiff
                    };
                    this.collectedFeatures.push(frameFeature);

                    console.log(`✅ 特征提取完成 - 帧 ${item.frameIndex}:`, {
                      colorDiff: features.colorDiff?.toFixed(4),
                      motionDiff: features.motionDiff?.toFixed(4),
                      textureDiff: features.textureDiff?.toFixed(4)
                    });
                  }
                  // VideoFrame已在批处理器内部关闭，这里不需要再关闭
                })
                .catch(error => {
                  // 如果是dispose错误，说明是正常的暂停操作，不记录为错误
                  if (error.name === 'FeatureExtractorDisposedError') {
                    console.log(`⏸️ 帧 ${item.frameIndex} 因暂停而停止处理`);
                  } else {
                    console.warn(`❌ 特征提取错误 - 帧 ${item.frameIndex}:`, error);
                  }
                  // VideoFrame已在批处理器内部关闭，即使出错也不需要再关闭
                });
            } else {
              // 单帧处理器：同步等待结果
              const features = await this.featureExtractor.processFrame(item.videoFrame, item.frameIndex);
              this.extractedFeatures++;

              if (features) {
                // 保存特征数据
                const frameFeature: FrameFeature = {
                  frameIndex: features.frameIndex,
                  timestamp: features.timestamp,
                  colorDiff: features.colorDiff,
                  motionDiff: features.motionDiff,
                  textureDiff: features.textureDiff
                };
                this.collectedFeatures.push(frameFeature);

                console.log(`✅ 特征提取完成 - 帧 ${item.frameIndex}:`, {
                  colorDiff: features.colorDiff?.toFixed(4),
                  motionDiff: features.motionDiff?.toFixed(4),
                  textureDiff: features.textureDiff?.toFixed(4)
                });
              }

              // 关闭VideoFrame释放内存
              item.videoFrame.close();
            }

          } catch (error) {
            console.warn('❌ 特征提取错误:', error);
            try {
              item.videoFrame.close();
            } catch (e) {
              // 忽略关闭错误
            }
          }
        } else {
          // 队列为空，检查是否完成
          if (this.decodingComplete && this.frameQueue.isEmpty()) {
            // 如果是批处理器，需要flush剩余帧
            if (this.featureExtractor && this.featureExtractor.flush) {
              console.log('🔄 Flush批处理器剩余帧...');
              await this.featureExtractor.flush();
            }

            this.featureEndTime = performance.now();
            this.featureExtractionComplete = true;
            this.isFeatureExtracting = false;
            console.log(`✅ 特征提取线程完成，共提取 ${this.extractedFeatures} 个特征`);

            // 启动分段处理
            this.performSegmentation().catch((error: Error) => {
              console.error('❌ 分段处理启动失败:', error);
              this.callbacks.onError(error);
            });
            break;
          } else {
            // 等待更长时间再检查，避免频繁日志
            await new Promise(resolve => setTimeout(resolve, 100)); // 从10ms增加到100ms
          }
        }
      }
    };

    // 启动队列处理
    processQueue().catch(error => {
      console.error('❌ 特征提取线程错误:', error);
      this.callbacks.onError(error);
    });
  }

  // 音频处理线程
  private async startAudioProcessingThread(file: File, asrConfig?: ASRConfig): Promise<void> {
    console.log('🎵 启动音频处理线程...');
    this.isAudioProcessing = true;
    this.audioStartTime = performance.now();

    try {
      // 创建音频处理器
      const audioCallbacks: AudioProcessingCallbacks = {
        onProgress: (status: string) => {
          console.log('🎵 音频处理进度:', status);
          if (this.callbacks.onAudioProgress) {
            this.callbacks.onAudioProgress(status);
          }
        },
        onComplete: (result: ASRResult) => {
          this.audioEndTime = performance.now();
          this.asrResult = result;
          this.audioProcessingComplete = true;
          this.isAudioProcessing = false;

          const audioTime = (this.audioEndTime - this.audioStartTime) / 1000;
          console.log(`✅ 音频处理完成 (${audioTime.toFixed(2)}s):`, {
            sentences: result.sentences?.length || 0,
            hasAudio: (result.sentences?.length || 0) > 0
          });

          if (this.callbacks.onAudioComplete) {
            this.callbacks.onAudioComplete(result);
          }

          this.checkAllComplete().catch(error => {
            console.error('❌ 完成检查失败:', error);
            this.callbacks.onError(error);
          });
        },
        onError: (error: Error) => {
          this.audioEndTime = performance.now();
          this.audioProcessingComplete = true; // 即使失败也标记为完成，不阻塞其他处理
          this.isAudioProcessing = false;
          console.error('❌ 音频处理失败:', error);

          if (this.callbacks.onAudioError) {
            this.callbacks.onAudioError(error);
          }

          this.checkAllComplete().catch(checkError => {
            console.error('❌ 完成检查失败:', checkError);
            this.callbacks.onError(checkError);
          });
        }
      };

      this.audioProcessor = new AudioProcessor(this.taskId, audioCallbacks);

      // 使用默认配置如果没有提供
      const finalAsrConfig = asrConfig || AudioProcessor.getDefaultASRConfig();

      // 开始处理音频
      await this.audioProcessor.processVideoAudio(file, finalAsrConfig);

    } catch (error) {
      console.error('❌ 音频处理线程启动失败:', error);
      this.audioProcessingComplete = true; // 标记为完成，不阻塞其他处理
      this.isAudioProcessing = false;

      if (this.callbacks.onAudioError) {
        this.callbacks.onAudioError(error as Error);
      }
    }
  }

  // 执行算法分段处理
  private async performSegmentation(): Promise<void> {
    console.log('🎯 开始算法分段处理...');
    this.isSegmentationProcessing = true;
    this.segmentationStartTime = performance.now();

    try {
      // 按帧索引排序特征数据
      const sortedFeatures = this.collectedFeatures.sort((a, b) => a.frameIndex - b.frameIndex);

      // 如果有特征数据，进行分段处理
      if (sortedFeatures.length > 1) {
        console.log(`🔄 开始分段处理 (${this.algorithmType})...`);

        // 转换为FrameDifference格式
        const frameDifferences = sortedFeatures.map(feature => ({
          colorDiff: feature.colorDiff,
          motionDiff: feature.motionDiff,
          textureDiff: feature.textureDiff,
          frameIndex: feature.frameIndex,
          timestamp: feature.timestamp
        }));

        // 配置算法参数
        const segmentConfig = {
          min_avg_duration: this.algorithmConfig.min_avg_duration,
          max_avg_duration: this.algorithmConfig.max_avg_duration,
          initial_pen: this.algorithmConfig.initial_pen,
          max_iterations: this.algorithmConfig.max_iterations,
          min_size: this.algorithmConfig.min_size
        };

        // 根据算法类型选择处理器
        if (this.algorithmType === 'go') {
          const goAlgorithmProcessor = getGoAlgorithmProcessor();
          this.segmentResult = await goAlgorithmProcessor.processFeatures(frameDifferences, segmentConfig);
        } else {
          const algorithmProcessor = getAlgorithmProcessor();
          this.segmentResult = await algorithmProcessor.processFeatures(frameDifferences, segmentConfig);
        }

        console.log('✅ 分段处理完成:', this.segmentResult);
      } else {
        console.log('⚠️ 特征数据不足，跳过分段处理');
        this.segmentResult = {
          segments: [],
          debug_info: { iterations: 0, final_pen: 0, avg_durations: [], segment_counts: [] },
          combined_scores: [],
          success: false,
          error: '特征数据不足'
        };
      }

      this.segmentationEndTime = performance.now();
      this.segmentationComplete = true;
      this.isSegmentationProcessing = false;

      const segmentationTime = (this.segmentationEndTime - this.segmentationStartTime) / 1000;
      console.log(`✅ 算法分段完成 (${segmentationTime.toFixed(2)}s)`);

      // 检查是否可以进行融合
      this.checkAllComplete().catch(error => {
        console.error('❌ 完成检查失败:', error);
        this.callbacks.onError(error);
      });

    } catch (error) {
      console.error('❌ 分段处理失败:', error);
      this.segmentationComplete = true; // 即使失败也标记为完成，不阻塞融合
      this.isSegmentationProcessing = false;
      this.segmentResult = {
        segments: [],
        debug_info: { iterations: 0, final_pen: 0, avg_durations: [], segment_counts: [] },
        combined_scores: [],
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };

      this.checkAllComplete().catch(checkError => {
        console.error('❌ 完成检查失败:', checkError);
        this.callbacks.onError(checkError);
      });
    }
  }

  // 检查所有处理是否完成
  private async checkAllComplete(): Promise<void> {
    const status = {
      decodingComplete: this.decodingComplete,
      featureExtractionComplete: this.featureExtractionComplete,
      audioProcessingComplete: this.audioProcessingComplete,
      queueSize: this.frameQueue.size()
    };

    console.log('🔍 检查完成状态:', status);

    // 如果特征提取完成但分段未完成，进行算法分段
    if (this.decodingComplete && this.featureExtractionComplete && !this.segmentationComplete) {
      console.log('🔄 特征提取完成，开始算法分段处理...');
      await this.performSegmentation();
      return; // 分段完成后会再次调用checkAllComplete
    }

    // 显示等待状态
    const waitingFor = [];
    if (!this.segmentationComplete) waitingFor.push('算法分段');
    if (!this.audioProcessingComplete) waitingFor.push('音频处理');

    if (waitingFor.length > 0) {
      console.log(`⏳ 等待处理完成: ${waitingFor.join(', ')}`);
      return; // 还有未完成的处理，直接返回
    }

    // 算法分段和音频处理都完成，开始音视频融合
    console.log('✅ 算法分段和音频处理完成，开始音视频融合...');

    if (this.segmentationComplete && this.audioProcessingComplete) {
      const endTime = performance.now();
      const totalTime = (endTime - this.startTime) / 1000;
      const decodingTime = (this.decodingEndTime - this.decodingStartTime) / 1000;
      const featureTime = this.featureExtractor ?
        (this.featureEndTime - this.featureStartTime) / 1000 : 0;
      const audioTime = this.audioEndTime > this.audioStartTime ?
        (this.audioEndTime - this.audioStartTime) / 1000 : 0;
      const segmentationTime = this.segmentationEndTime > this.segmentationStartTime ?
        (this.segmentationEndTime - this.segmentationStartTime) / 1000 : 0;

      // 使用已经计算好的分段结果
      const segmentResult = this.segmentResult;

      // 提取分段时间点
      let segmentTimes: number[] = segmentResult?.segments || [];

      // 确保分段时间点包含视频开始(0)和结束时间
      if (segmentTimes.length > 0) {
        // 获取视频结束时间（从解码器的视频信息中获取）
        const videoInfo = this.decoder?.getVideoInfo();
        const videoDuration = videoInfo?.duration || 0;

        // 修改第一个时间点为0（如果不是0的话）
        if (segmentTimes[0] !== 0) {
          segmentTimes[0] = 0;
          console.log('🔧 修正第一个分段时间点为 0');
        }

        // 修改最后一个时间点为视频结束时间（如果不是结束时间的话）
        if (videoDuration > 0 && segmentTimes[segmentTimes.length - 1] !== videoDuration) {
          segmentTimes[segmentTimes.length - 1] = videoDuration;
          console.log(`🔧 修正最后一个分段时间点为 ${videoDuration}`);
        }

        console.log('📊 修正后的分段时间点:', segmentTimes);
      }

      // 进行音视频融合处理
      let fusionResult: FusionResult | undefined;
      let segmentMetadata: SegmentMetadataRequest | undefined;

      try {
        console.log('🔄 开始音视频融合处理...');
        fusionResult = this.fusionProcessor.fuseBoundaries(segmentTimes, this.asrResult || undefined);

        // 构建分段元数据（需要视频信息和文件）
        if (this.currentFile && fusionResult.adjustedBoundaries.length > 1) {
          console.log('🏗️ 开始构建分段元数据...');

          // 构建视频信息（从当前处理状态获取）
          const videoInfo = {
            width: 0, // 这些值需要从解码器获取，暂时使用默认值
            height: 0,
            duration: fusionResult.adjustedBoundaries[fusionResult.adjustedBoundaries.length - 1],
            frameRate: this.currentTargetFps || 5,
            codec: 'unknown'
          };

          segmentMetadata = this.metadataBuilder.buildSegmentMetadata(
            fusionResult.adjustedBoundaries,
            videoInfo,
            this.currentFile,
            this.asrResult || undefined
          );

          // 验证元数据
          const validation = this.metadataBuilder.validateMetadata(segmentMetadata);
          if (!validation.valid) {
            console.warn('⚠️ 元数据验证失败:', validation.errors);
          } else {
            console.log('✅ 元数据验证通过');

            // 上传元数据到后端
            try {
              console.log('📤 开始上传元数据到后端...');
              const uploadResponse = await this.metadataBuilder.uploadMetadata(segmentMetadata, this.taskId);
              console.log('✅ 元数据上传成功:', uploadResponse.message);

              // 元数据上传成功后，开始流式分段帧上传
              if (this.currentFile && this.segmentFrameUploader) {
                try {
                  console.log('📤 元数据上传成功，开始流式分段帧上传...');

                  // 更新上传器的配置（之前使用的是临时值）
                  console.log('🔧 更新流式上传器配置...');
                  this.segmentFrameUploader.updateTaskId(this.taskId);
                  this.segmentFrameUploader.updateVideoCode(uploadResponse.video_code);

                  // 开始流式上传（先发送请求头，然后逐个发送片段）
                  await this.segmentFrameUploader.uploadSegmentFrames(this.currentFile, segmentMetadata);

                  console.log('✅ 流式分段帧上传完成，开始章节聚合...');

                  // 流式上传完成后，开始章节聚合
                  if (this.chapterAggregator) {
                    try {
                      console.log('📚 开始章节聚合，视频代码:', uploadResponse.video_code);
                      await this.chapterAggregator.aggregateChapters(this.taskId, [uploadResponse.video_code]);
                      console.log('✅ 章节聚合完成');
                    } catch (chapterError) {
                      console.error('❌ 章节聚合失败:', chapterError);
                      // 章节聚合失败不阻塞主流程，只记录错误
                    }
                  } else {
                    console.warn('⚠️ 章节聚合器未初始化，跳过章节聚合');
                  }

                } catch (frameUploadError) {
                  console.error('❌ 流式分段帧上传失败:', frameUploadError);
                  // 流式分段帧上传失败不阻塞主流程，只记录错误
                }
              } else if (!this.segmentFrameUploader) {
                console.warn('⚠️ 流式分段帧上传器未初始化，跳过帧上传');
              }
            } catch (uploadError) {
              console.error('❌ 元数据上传失败:', uploadError);
              // 上传失败不阻塞主流程，只记录错误
            }
          }
        }
      } catch (error) {
        console.error('❌ 融合或元数据构建失败:', error);
        // 继续处理，不阻塞完成回调
      }

      const stats: ProcessingStats = {
        totalFrames: this.processedFrames,
        totalTime: totalTime,
        avgTimePerFrame: (totalTime / this.processedFrames) * 1000,
        decodingTime: decodingTime,
        featureExtractionTime: featureTime,
        segmentationTime: segmentationTime,
        segmentTimes: segmentTimes,
        audioProcessingTime: audioTime,
        asrResult: this.asrResult || undefined,
        fusionResult: fusionResult,
        segmentMetadata: segmentMetadata
      };

      console.log('🎉 所有处理完成！', stats);
      this.callbacks.onComplete(stats);
    }
  }

  // 停止处理
  stop(): void {
    this.isFeatureExtracting = false;
    this.isAudioProcessing = false;
    this.frameQueue.clear();
  }

  // 获取队列状态
  getQueueStatus(): { size: number; isEmpty: boolean } {
    return {
      size: this.frameQueue.size(),
      isEmpty: this.frameQueue.isEmpty()
    };
  }

  // 设置融合配置
  setFusionConfig(config: Partial<FusionConfig>): void {
    this.fusionProcessor = new FusionProcessor(config);
    console.log('🔧 融合配置已更新:', config);
  }

  // 设置元数据构建配置
  setMetadataBuilderConfig(config: Partial<MetadataBuilderConfig>): void {
    this.metadataBuilder = new MetadataBuilder(config);
    console.log('🔧 元数据构建配置已更新:', config);
  }

  // 获取当前融合配置
  getFusionConfig(): FusionConfig {
    return FusionProcessor.getDefaultConfig();
  }

  // 获取当前元数据构建配置
  getMetadataBuilderConfig(): MetadataBuilderConfig {
    return MetadataBuilder.getDefaultConfig();
  }

  // 清理资源
  dispose(): void {
    this.stop();
    if (this.featureExtractor) {
      this.featureExtractor.dispose();
    }
    if (this.decoder) {
      this.decoder.dispose();
    }
    if (this.segmentFrameUploader) {
      this.segmentFrameUploader.dispose();
      this.segmentFrameUploader = null;
    }
    if (this.chapterAggregator) {
      this.chapterAggregator.dispose();
      this.chapterAggregator = null;
    }
    // 音频处理器不需要特殊清理，会在处理完成或错误时自动清理

    // 清理可见性监听器
    if (this.visibilityListener && typeof window !== 'undefined' && (window as any).visibilityManager) {
      (window as any).visibilityManager.removeListener(this.visibilityListener);
      this.visibilityListener = null;
    }
  }
}
