:root {
    --el-color-primary: #7B61FF !important;
    --el-button-text-color: #fff !important;
    --el-color-primary-light-3: #6A50E0 !important;

    /* 完全复制源项目的CSS变量 */
    --primary: #7B61FF; /* 主紫色 */
    --primary-dark: #6A50E0; /* 深紫色（hover） */
    --bg-light: #F5F7FA; /* 浅灰背景 */
    --text-dark: #333333; /* 主文本 */
    --text-light: #666666; /* 次要文本 */
    --white: #FFFFFF;
    --shadow: 0 4px 20px rgba(0,0,0,0.08); /* 卡片阴影 */

    font-family: 'Inter', -apple-system, sans-serif;
    line-height: 1.6;
    font-weight: 400;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}
::-webkit-scrollbar-track {
    background: var(--bg-light);
}
::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* 渐变背景类 */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

/* 文本渐变 */
.text-gradient {
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    background-image: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

/* 全局动画定义 */
@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@tailwind base;
@tailwind components;
@tailwind utilities;