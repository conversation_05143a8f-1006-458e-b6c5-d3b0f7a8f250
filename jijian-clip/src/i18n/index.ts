import { createI18n } from 'vue-i18n'

// 导入语言包
import zh from './locales/zh.json'
import en from './locales/en.json'

// 检测用户浏览器语言偏好或使用保存的偏好
const savedLanguage = localStorage.getItem('jijian_language')
const userLanguage = navigator.language.split('-')[0]
const initialLanguage = savedLanguage || (['zh', 'en'].includes(userLanguage) ? userLanguage : 'zh')

// 创建i18n实例
const i18n = createI18n({
  legacy: false, // 使用 Composition API 模式
  locale: initialLanguage, // 设置初始语言
  fallbackLocale: 'zh', // 回退语言
  messages: {
    zh,
    en
  },
  globalInjection: true, // 全局注入
})

export default i18n

// 导出切换语言的方法
export const switchLanguage = (locale: string) => {
  i18n.global.locale.value = locale
  localStorage.setItem('jijian_language', locale)
}

// 导出当前语言
export const getCurrentLanguage = () => {
  return i18n.global.locale.value
}
