/**
 * 项目入口文件
 * 配置Vue应用的全局设置，包括路由、状态管理、UI组件库等
 */

import { createApp } from 'vue';
import router from './router';
import { setupStore } from '@/store';
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import enUs from 'element-plus/es/locale/lang/en';

// 导入国际化配置
import i18n, { getCurrentLanguage } from '@/i18n';

// ElementPlus 样式导入
import 'element-plus/dist/index.css';
import 'element-plus/theme-chalk/dark/css-vars.css';

// 全局样式导入
import './styles/main.scss';
import './styles/rem.js'; // 自适应rem配置

import App from './App.vue';

// 全局指令注册
import { throttle } from '@/utils/directive';
import 'animate.css';

// 全局错误处理
window.addEventListener('error', (event) => {
  console.warn('🚨 全局错误捕获:', event.error);
  // 防止 SES 相关错误影响应用运行
  if (event.error === null || (event.message && event.message.includes('SES_UNCAUGHT_EXCEPTION'))) {
    console.warn('⚠️ 检测到 SES 相关错误，已忽略');
    event.preventDefault();
    return false;
  }
});

window.addEventListener('unhandledrejection', (event) => {
  console.warn('🚨 未处理的 Promise 拒绝:', event.reason);

  // 特殊处理WebAV相关错误
  if (event.reason?.message?.includes('MP4Clip stream is done, but not emit ready')) {
    console.error('🎬 WebAV MP4Clip错误 - 可能是视频文件格式问题或文件损坏');
    // 这里不阻止事件，让错误继续传播以便调试
    return;
  }

  // 处理文件写入器错误
  if (event.reason?.message?.includes('Other writer have not been closed')) {
    console.error('📝 WebAV文件写入器错误 - 可能是文件写入器没有正确关闭');
    // 阻止这个错误的默认处理，因为我们已经在代码中处理了
    event.preventDefault();
    return;
  }

  // 处理Canvas相关错误
  if (event.reason?.message?.includes('Canvas') || event.reason?.message?.includes('Sprite')) {
    console.error('🎨 Canvas/Sprite错误 - 可能是渲染问题');
    return;
  } else if (event.reason?.message?.includes('QuotaExceededError')) {
    console.error('💾 存储配额超限错误');
    event.preventDefault();
    return false;
  } else if (event.reason?.message?.includes('webcodecs')) {
    console.error('🔧 WebCodecs处理错误:', event.reason);
    // 这里不阻止事件，让错误继续传播以便调试
    return;
  }

  // 防止常见的开发环境错误影响应用
  if (event.reason === null ||
      (event.reason instanceof TypeError &&
       (event.reason.message.includes('Cannot read properties of undefined') ||
        event.reason.message.includes('Cannot destructure property') ||
        event.reason.message.includes('vnode')))) {
    console.warn('⚠️ 检测到开发环境常见错误，已忽略:', event.reason);
    event.preventDefault();
    return false;
  }
});

// 创建Vue应用实例
const Vue = createApp(App);

// 配置Vuex状态管理
setupStore(Vue);

// 注册全局节流指令
Vue.directive('throttle', throttle);

// 注册路由
Vue.use(router);

// 注册国际化
Vue.use(i18n);

// 配置ElementPlus，根据当前语言设置语言包
const currentLang = getCurrentLanguage();
const elementLocale = currentLang === 'en' ? enUs : zhCn;
Vue.use(ElementPlus, { locale: elementLocale });

// 挂载应用
Vue.mount('#app');

// 注册所有ElementPlus图标组件
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  Vue.component(key, component);
}
