/**
 * 路由配置文件
 * 使用Vue Router管理应用的路由
 */

import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
import { ElMessage } from 'element-plus';

/**
 * 路由配置数组
 * 定义应用的所有路由规则
 */
const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/home', // 根路径重定向到新主页
  },
  {
    path: '/home',
    component: () => import('@/views/jijian-home/index.vue'), // 即剪AI新主页
    meta: { requiresAuth: false }, // 首页不需要登录
  },
  {
    path: '/projects',
    component: () => import('@/views/projectList/index.vue'), // 项目列表页面（保留原有功能）
    meta: { requiresAuth: false }, // 首页不需要登录
  },
  {
    path: '/upload',
    component: () => import('@/views/jijian-upload/index.vue'), // 即剪AI新上传页面
    meta: { requiresAuth: false }, // 测试环境无需登录验证
  },
  {
    path: '/upload-legacy',
    component: () => import('@/views/upload/index.vue'), // 原有上传页面（保留功能）
    meta: { requiresAuth: false }, // 测试环境无需登录验证
  },
  {
    path: '/processing/:id',
    component: () => import('@/views/processing/[id].vue'), // 视频处理进度页面
    meta: { requiresAuth: false }, // 测试环境无需登录验证
  },
  {
    path: '/clip/:id',
    component: () => import('@/views/clipPage/index.vue'), // 视频剪辑页面，使用动态路由参数id
    meta: { requiresAuth: false }, // 测试环境无需登录验证
  },
  {
    path: '/clip-upgrade/:id',
    component: () => import('@/views/clipPage/ClipPageWithUpgrade.vue'), // 带升级功能的视频剪辑页面
    meta: { requiresAuth: false }, // 测试环境无需登录验证
  },
  {
    path: '/refactor-demo',
    component: () => import('@/views/refactor-demo.vue'), // 重构版本演示页面
  },
  {
    path: '/ai-demo',
    component: () => import('@/views/ai-demo/index.vue'), // AI 助手演示页面
    meta: { requiresAuth: false },
  },
  {
    path: '/timeline-demo',
    component: () => import('@/views/editor/ProgressiveTimelineDemo.vue'), // 渐进式时间轴演示页面
    meta: { requiresAuth: false },
  },
  {
    path: '/fcpxml-test',
    component: () => import('@/views/test/FCPXMLTest.vue'), // FCPXML导出器测试页面
    meta: { requiresAuth: false },
  },
  // 认证相关路由
  {
    path: '/auth',
    children: [
      {
        path: 'login',
        component: () => import('@/views/auth/login.vue'),
        meta: { requiresAuth: false, hideForAuth: true }, // 已登录用户不显示
      },
      {
        path: 'register',
        component: () => import('@/views/auth/register.vue'),
        meta: { requiresAuth: false, hideForAuth: true }, // 已登录用户不显示
      },
    ],
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/exception/404.vue'), // 404页面，处理未匹配的路由
  },
];

/**
 * 创建路由实例
 * 使用hash模式的路由历史
 */
const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

/**
 * 路由守卫
 * 处理登录验证和权限控制
 * 测试环境：简化登录验证逻辑
 */
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore();

  // 测试环境：如果访问需要登录的页面但未登录，自动创建测试用户
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    console.log('🧪 测试环境：自动登录测试用户');

    // 创建测试用户状态
    const testUser = {
      id: 'test-user-001',
      username: 'TestUser',
      email: '<EMAIL>',
      avatar: '',
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString()
    };

    const testToken = 'mock_token_test_user_' + Date.now();

    // 保存测试用户状态
    userStore.token = testToken;
    userStore.userInfo = testUser;
    userStore.isLoggedIn = true;

    localStorage.setItem('jijian_token', testToken);
    localStorage.setItem('jijian_user_info', JSON.stringify(testUser));

    console.log('✅ 测试用户登录成功:', testUser.username);
  }

  // 如果已登录用户访问登录/注册页面，重定向到项目页面
  if (to.meta.hideForAuth && userStore.isLoggedIn) {
    next('/projects');
    return;
  }

  next();
});

export default router;
