/* 导入统一字体系统 */
@import './styles/typography.css';

:root {
  /* 即剪AI主题色彩系统 - 继承自268616744450项目 */
  --primary: #7B61FF; /* 主紫色 */
  --primary-dark: #6A50E0; /* 深紫色（hover） */
  --bg-light: #F5F7FA; /* 浅灰背景 */
  --text-dark: #333333; /* 主文本 */
  --text-light: #666666; /* 次要文本 */
  --white: #FFFFFF;
  --shadow: 0 4px 20px rgba(0,0,0,0.08); /* 卡片阴影 */

  /* 使用统一的字体系统 */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  font-size: var(--text-base);
  line-height: 1.6;
  font-weight: 400;

  color-scheme: light dark;
  color: var(--text-dark);
  background-color: var(--white);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;

  /* 保留原有视频编辑界面主题变量（向后兼容） */
  --color-primary: var(--primary);
  --color-primary-hover: var(--primary-dark);
  --color-secondary: #374151;
  --color-bg-primary: #1a1a1a;
  --color-bg-secondary: #2a2a2a;
  --color-bg-hover: #3a3a3a;
  --color-border: #4b5563;
  --color-text-primary: #ffffff;
  --color-text-secondary: #d1d5db;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  background: var(--bg-light);
}
::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 全局动画定义 */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* 渐变背景类 */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

/* 文本渐变 */
.text-gradient {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

/* 即剪AI品牌样式 */
.jijian-brand {
  font-family: 'Inter', -apple-system, sans-serif;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* 卡片样式 */
.jijian-card {
  background: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.jijian-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

/* 按钮样式 */
.jijian-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: none;
  outline: none;
}

.jijian-btn-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: var(--white);
  box-shadow: 0 4px 12px rgba(123, 97, 255, 0.3);
}

.jijian-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(123, 97, 255, 0.4);
}

@media (prefers-color-scheme: light) {
  :root {
    color: var(--text-dark);
    background-color: var(--white);
  }
  a:hover {
    color: var(--primary);
  }
  button {
    background-color: var(--bg-light);
  }
}
