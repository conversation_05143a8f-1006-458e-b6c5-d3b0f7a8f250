@tailwind base;
@tailwind components;
@tailwind utilities;

/* 导入统一字体系统 */
@import './styles/typography.css';

:root {
  /* 即剪AI主题色彩系统 - 完全复制自268616744450项目 */
  --primary: #7B61FF; /* 主紫色 */
  --primary-dark: #6A50E0; /* 深紫色（hover） */
  --bg-light: #F5F7FA; /* 浅灰背景 */
  --text-dark: #333333; /* 主文本 */
  --text-light: #666666; /* 次要文本 */
  --white: #FFFFFF;
  --shadow: 0 4px 20px rgba(0,0,0,0.08); /* 卡片阴影 */

  font-family: 'Inter', -apple-system, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* 保留原有视频编辑界面主题变量（向后兼容） */
  --color-primary: var(--primary);
  --color-primary-hover: var(--primary-dark);
  --color-secondary: #374151;
  --color-bg-primary: #1a1a1a;
  --color-bg-secondary: #2a2a2a;
  --color-bg-hover: #3a3a3a;
  --color-border: #4b5563;
  --color-text-primary: #ffffff;
  --color-text-secondary: #d1d5db;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

/* 自定义滚动条 - 完全复制自源项目 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  background: var(--bg-light);
}
::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 全局动画定义 - 完全复制自源项目 */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* 渐变背景类 - 完全复制自源项目 */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

/* 文本渐变 - 完全复制自源项目 */
.text-gradient {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

/* 源项目样式类 - 确保完全兼容 */
.header {
  backdrop-filter: blur(8px);
}

.prompt-input {
  resize: none;
  transition: all 0.3s ease;
}

.prompt-input:focus {
  outline: none;
  ring: 2px;
  ring-color: var(--primary);
}

.input-progress {
  transition: width 0.3s ease;
}

.char-counter {
  font-size: 0.875rem;
  color: var(--text-light);
}

.action-btn {
  transition: all 0.3s ease;
}

.action-btn:hover {
  background-color: #f9fafb;
}

.generate-btn {
  transition: all 0.3s ease;
}

.generate-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
}

/* 项目卡片样式 */
.project-card {
  transition: all 0.5s ease;
}

.project-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

/* 功能卡片样式 */
.feature-card {
  transition: all 0.5s ease;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

/* 行截断样式 */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

@media (prefers-color-scheme: light) {
  :root {
    color: var(--text-dark);
    background-color: var(--white);
  }
  a:hover {
    color: var(--primary);
  }
  button {
    background-color: var(--bg-light);
  }
}
