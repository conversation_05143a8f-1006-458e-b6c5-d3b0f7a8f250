<!DOCTYPE html>
<html lang="en" class="dark">

<head>
	<meta charset="UTF-8" />
	<link rel="icon" href="/favicon.ico" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>即剪AI</title>

	<!-- MP4Box 库 CDN 预加载（备用方案） -->
	<script>
		// 预加载 MP4Box 库，避免动态加载时的延迟
		(function() {
			const script = document.createElement('script');
			script.src = 'https://unpkg.com/mp4box@0.5.2/dist/mp4box.all.min.js';
			script.async = true;
			script.onload = function() {
				console.log('✅ MP4Box CDN 预加载成功');
			};
			script.onerror = function() {
				console.warn('⚠️ MP4Box CDN 预加载失败，将使用动态加载');
			};
			document.head.appendChild(script);
		})();
	</script>

	<!-- 🎯 TensorFlow.js 库预加载（用于特征提取） -->
	<script>
		// 预加载 TensorFlow.js 库，支持 WebCodecs 特征提取
		(function() {
			const script = document.createElement('script');
			script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@latest/dist/tf.min.js';
			script.async = true;
			script.onload = function() {
				console.log('✅ TensorFlow.js CDN 预加载成功');
				// 验证 tf 对象是否可用
				if (typeof tf !== 'undefined') {
					console.log('🎯 TensorFlow.js 版本:', tf.version.tfjs);
				}
			};
			script.onerror = function() {
				console.warn('⚠️ TensorFlow.js CDN 预加载失败，特征提取将使用降级方案');
			};
			document.head.appendChild(script);
		})();
	</script>
</head>

<body>
	<div id="app"></div>
	<script type="module" src="/src/main.ts"></script>
	<script type="text/javascript">
		//js判断是mac还是windows系统
		function WhichSystem() {
			var agent = navigator.userAgent.toLowerCase()
			var isMac = /macintosh|mac os x/i.test(navigator.userAgent)
			let node = document.querySelector('body')
			console.log(isMac, '是否是MAC系统')
			console.log(document.querySelector('body'), 'body节点样式')
			if (isMac) {
				node.style.fontFamily = "PingFang SC"
			} else {
				node.style.fontFamily = "黑体"
			}
		}
		WhichSystem();
	</script>
</body>

</html>