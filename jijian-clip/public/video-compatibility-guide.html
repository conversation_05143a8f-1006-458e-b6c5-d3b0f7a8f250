<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频兼容性指南</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            min-height: 100vh;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .title {
            font-size: 28px;
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 12px;
        }

        .subtitle {
            font-size: 16px;
            color: #888888;
        }

        .section {
            background: #262626;
            border: 1px solid #333333;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .problem-box {
            background: #2d1b1b;
            border: 1px solid #ef4444;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .solution-box {
            background: #1b2d1b;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .warning-box {
            background: #2d2416;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .format-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin: 16px 0;
        }

        .format-card {
            background: #1f1f1f;
            border: 1px solid #333333;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }

        .format-card.supported {
            border-color: #10b981;
        }

        .format-card.problematic {
            border-color: #ef4444;
        }

        .format-card.limited {
            border-color: #f59e0b;
        }

        .format-name {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .format-desc {
            font-size: 12px;
            color: #888888;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-top: 8px;
        }

        .status-supported {
            background: #10b981;
            color: white;
        }

        .status-problematic {
            background: #ef4444;
            color: white;
        }

        .status-limited {
            background: #f59e0b;
            color: white;
        }

        .tool-list {
            list-style: none;
        }

        .tool-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #333333;
        }

        .tool-item:last-child {
            border-bottom: none;
        }

        .tool-icon {
            width: 40px;
            height: 40px;
            background: #333333;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .tool-info {
            flex: 1;
        }

        .tool-name {
            font-size: 14px;
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 4px;
        }

        .tool-desc {
            font-size: 12px;
            color: #888888;
        }

        .btn {
            background: #0066cc;
            color: #ffffff;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            margin: 8px 8px 8px 0;
        }

        .btn:hover {
            background: #0052a3;
            transform: translateY(-1px);
        }

        .btn.secondary {
            background: #333333;
        }

        .btn.secondary:hover {
            background: #404040;
        }

        .code-block {
            background: #0a0a0a;
            border: 1px solid #333333;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 12px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎬 视频兼容性指南</h1>
            <p class="subtitle">解决视频处理中的格式兼容性问题</p>
        </div>

        <!-- 常见问题 -->
        <div class="section">
            <h2 class="section-title">❌ 常见问题</h2>
            
            <div class="problem-box">
                <h4 style="color: #fca5a5; margin-bottom: 8px;">MP4Box 解码错误</h4>
                <p style="font-size: 14px; color: #cccccc;">
                    错误信息：<code>Cannot read properties of undefined (reading 'findPosition')</code>
                </p>
                <p style="font-size: 12px; color: #888888; margin-top: 8px;">
                    这通常发生在处理某些 H.265/HEVC 编码的视频文件时，MP4Box 库无法正确解析文件结构。
                </p>
            </div>

            <div class="problem-box">
                <h4 style="color: #fca5a5; margin-bottom: 8px;">H.265 描述信息缺失</h4>
                <p style="font-size: 14px; color: #cccccc;">
                    错误信息：<code>No description found for codec: hvc1.1.6.L123.b0</code>
                </p>
                <p style="font-size: 12px; color: #888888; margin-top: 8px;">
                    H.265 视频需要特定的编码描述信息，某些编码器生成的文件可能缺少这些信息。
                </p>
            </div>
        </div>

        <!-- 支持的格式 -->
        <div class="section">
            <h2 class="section-title">✅ 支持的视频格式</h2>
            
            <div class="format-grid">
                <div class="format-card supported">
                    <div class="format-name">H.264 (AVC)</div>
                    <div class="format-desc">最佳兼容性，推荐使用</div>
                    <div class="status-badge status-supported">完全支持</div>
                </div>
                
                <div class="format-card limited">
                    <div class="format-name">H.265 (HEVC)</div>
                    <div class="format-desc">部分支持，取决于编码参数</div>
                    <div class="status-badge status-limited">有限支持</div>
                </div>
                
                <div class="format-card supported">
                    <div class="format-name">VP8</div>
                    <div class="format-desc">WebM 容器中的 VP8</div>
                    <div class="status-badge status-supported">支持</div>
                </div>
                
                <div class="format-card supported">
                    <div class="format-name">VP9</div>
                    <div class="format-desc">WebM 容器中的 VP9</div>
                    <div class="status-badge status-supported">支持</div>
                </div>
            </div>

            <div class="solution-box">
                <h4 style="color: #86efac; margin-bottom: 8px;">推荐设置</h4>
                <div class="code-block">
编码格式: H.264 (x264)
容器格式: MP4
分辨率: 1920x1080 或更低
帧率: 30fps 或更低
码率: 5-10 Mbps
音频: AAC, 48kHz, 立体声</div>
            </div>
        </div>

        <!-- 解决方案 -->
        <div class="section">
            <h2 class="section-title">🔧 解决方案</h2>
            
            <div class="solution-box">
                <h4 style="color: #86efac; margin-bottom: 12px;">方案1: 使用兼容的视频文件</h4>
                <p style="font-size: 14px; margin-bottom: 8px;">尝试使用 H.264 编码的 MP4 文件，这是兼容性最好的格式。</p>
            </div>

            <div class="solution-box">
                <h4 style="color: #86efac; margin-bottom: 12px;">方案2: 视频格式转换</h4>
                <p style="font-size: 14px; margin-bottom: 12px;">使用以下工具将视频转换为兼容格式：</p>
                
                <ul class="tool-list">
                    <li class="tool-item">
                        <div class="tool-icon">🎬</div>
                        <div class="tool-info">
                            <div class="tool-name">FFmpeg (命令行)</div>
                            <div class="tool-desc">专业的视频转换工具，支持所有格式</div>
                        </div>
                    </li>
                    <li class="tool-item">
                        <div class="tool-icon">🖥️</div>
                        <div class="tool-info">
                            <div class="tool-name">HandBrake (图形界面)</div>
                            <div class="tool-desc">免费的开源视频转换器，易于使用</div>
                        </div>
                    </li>
                    <li class="tool-item">
                        <div class="tool-icon">🌐</div>
                        <div class="tool-info">
                            <div class="tool-name">在线转换工具</div>
                            <div class="tool-desc">CloudConvert, Online-Convert 等</div>
                        </div>
                    </li>
                </ul>
            </div>

            <div class="warning-box">
                <h4 style="color: #fbbf24; margin-bottom: 8px;">FFmpeg 转换命令示例</h4>
                <div class="code-block">
# 转换为 H.264 MP4
ffmpeg -i input.mp4 -c:v libx264 -c:a aac -preset medium -crf 23 output.mp4

# 降低分辨率和码率
ffmpeg -i input.mp4 -c:v libx264 -c:a aac -vf scale=1280:720 -b:v 2M output.mp4</div>
            </div>
        </div>

        <!-- 技术细节 -->
        <div class="section">
            <h2 class="section-title">🔍 技术细节</h2>
            
            <h4 style="margin-bottom: 12px;">为什么会出现兼容性问题？</h4>
            <ul style="margin-left: 20px; margin-bottom: 16px;">
                <li style="margin-bottom: 8px;">WebCodecs API 对某些编码格式的支持有限</li>
                <li style="margin-bottom: 8px;">MP4Box.js 库在解析某些文件结构时可能出错</li>
                <li style="margin-bottom: 8px;">H.265 编码的复杂性导致兼容性问题</li>
                <li style="margin-bottom: 8px;">不同编码器生成的文件结构差异</li>
            </ul>

            <h4 style="margin-bottom: 12px;">浏览器支持情况：</h4>
            <div class="code-block">
Chrome 94+: 完整的 WebCodecs 支持
Edge 94+:   完整的 WebCodecs 支持  
Firefox:    部分支持 (实验性)
Safari:     有限支持</div>
        </div>

        <!-- 操作按钮 -->
        <div class="section">
            <h2 class="section-title">🎯 下一步操作</h2>
            
            <a href="/#/upload" class="btn">返回上传页面</a>
            <a href="/debug-webcodecs.html" class="btn secondary">诊断工具</a>
            <button class="btn secondary" onclick="window.print()">打印指南</button>
        </div>
    </div>

    <script>
        // 检查是否从错误页面跳转过来
        const urlParams = new URLSearchParams(window.location.search);
        const errorType = urlParams.get('error');
        
        if (errorType) {
            // 根据错误类型高亮相关部分
            console.log('错误类型:', errorType);
        }

        // 添加复制代码功能
        document.querySelectorAll('.code-block').forEach(block => {
            block.addEventListener('click', () => {
                navigator.clipboard.writeText(block.textContent).then(() => {
                    const originalText = block.textContent;
                    block.textContent = '✅ 已复制到剪贴板';
                    setTimeout(() => {
                        block.textContent = originalText;
                    }, 2000);
                });
            });
        });
    </script>
</body>
</html>
