import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import AutoImport from "unplugin-auto-import/vite";
import { resolve } from "path";
import { createHtmlPlugin } from "vite-plugin-html";
import Inspector from "unplugin-vue-inspector/vite";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    // vueDevTools(),
    vue(),
    AutoImport({
      imports: ["vue", "vue-router"], //自动引入vue的ref、toRefs、onmounted等，无需在页面中再次引入
      dts: "src/auto-import.d.ts", // 生成在src路径下名为auto-import.d.ts的声明文件
    }),
    Inspector(),
    createHtmlPlugin(),
  ],
  resolve: {
    alias: [
      {
        find: "@",
        replacement: resolve(__dirname, "src"),
      },
    ],
  },
  base: "./", // 打包路径
  server: {
    host: "0.0.0.0", // 允许外部访问
    port: 5173,
    strictPort: false, // 端口被占用时自动尝试下一个端口
    open: false, // 不自动打开浏览器
    cors: true, // 启用 CORS
    headers: {
      "Cross-Origin-Opener-Policy": "same-origin",
      "Cross-Origin-Embedder-Policy": "require-corp",
      // 🎯 添加 CSP 头部，允许 CDN 脚本加载和 Web Workers
      "Content-Security-Policy": "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://cdn.jsdelivr.net; worker-src 'self' blob: data:; connect-src 'self' ws: wss: http: https: https://api.iconify.design https://api.unisvg.com https://api.simplesvg.com; frame-src 'self' data: blob: videocut: jianying: jianyingpro:;",
    },
    // 网络相关配置
    // hmr: {
    //   port: 8086, // HMR 使用不同端口
    //   host: "0.0.0.0"
    // }
  },
});
