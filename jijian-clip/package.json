{"name": "dataconfig", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "dev:network": "vite --mode dev --host 0.0.0.0", "build": "vite build --mode production", "build:test": "vite build --mode test", "preview": "vite preview --mode production", "test": "vitest", "network-check": "node scripts/network-diagnostic.js"}, "simple-git-hooks": {"commit-msg": "node scripts/verify-commit.js", "pre-commit": "pnpm lint-staged"}, "lint-staged": {"*.{vue,json,ts,tsx,js,md}": ["npx prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iconify-icons/mdi": "^1.2.48", "@iconify/vue": "^4.2.0", "@intlify/unplugin-vue-i18n": "^6.0.8", "@tailwindcss/line-clamp": "^0.4.4", "@tensorflow/tfjs": "^4.22.0", "@vue/shared": "^3.5.13", "@vueuse/core": "^9.13.0", "@vueuse/motion": "^3.0.3", "@vueuse/rxjs": "^11.2.0", "@webav/av-canvas": "1.1.0", "@webav/av-cliper": "1.1.0", "@webav/internal-utils": "1.1.0", "animate.css": "^4.1.1", "axios": "^0.27.2", "clsx": "^2.1.1", "concurrently": "^8.2.2", "dayjs": "^1.11.13", "dexie": "^4.0.10", "element-plus": "^2.8.8", "element-resize-detector": "^1.2.4", "framer-motion": "^12.23.12", "js-cookie": "^3.0.5", "jsdom": "^24.1.3", "lodash": "^4.17.21", "mp4box": "^1.4.2", "openai": "^4.104.0", "opfs-tools": "^0.7.2", "pinia": "^2.2.6", "pinia-plugin-persistedstate": "^2.4.0", "pixi.js": "7.4.2", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "split.js": "^1.6.5", "tailwind-merge": "^3.3.1", "unplugin-auto-import": "^0.11.5", "unplugin-vue-components": "^0.25.2", "uuid": "^9.0.1", "vite-plugin-static-copy": "^0.17.1", "vue": "^3.5.13", "vue-i18n": "^9.14.5", "vue-router": "^4.4.5"}, "devDependencies": {"@types/dom-webcodecs": "^0.1.13", "@types/lodash": "^4.17.13", "@types/node": "^18.19.64", "@vitejs/plugin-vue": "^5.2.0", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.20", "gh-pages": "^6.3.0", "postcss": "^8.4.49", "postcss-loader": "^8.1.1", "sass": "^1.81.0", "sass-loader": "^12.6.0", "simple-git-hooks": "^2.11.1", "tailwindcss": "^3.4.15", "terser": "^5.36.0", "typescript": "^4.9.5", "unplugin-vue-inspector": "^2.3.1", "vite": "^5.4.19", "vite-plugin-html": "^3.2.2", "vite-plugin-mkcert": "^1.17.6", "vite-plugin-style-import": "^2.0.0", "vite-plugin-vue-devtools": "^7.6.4", "vite-plugin-vue-inspector": "^5.3.1", "vue-tsc": "^0.40.13"}}